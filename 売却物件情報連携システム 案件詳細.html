<!DOCTYPE html PUBLIC "-//W3C//Dtd HTML 4.01 transitional//EN" "http://www.w3.org/tr/html4/loose.dtd">
<!-- saved from url=(0057)http://devap9001/BaibaiJohoKanri/detail?ankenNo=000000371 -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  
  <title>売却物件情報連携システム 案件詳細</title>

  
  <link href="./売却物件情報連携システム 案件詳細_files/common.css" rel="stylesheet">
  
  <link href="./売却物件情報連携システム 案件詳細_files/jquery-ui.min.css" rel="stylesheet">

  
  <script src="./売却物件情報連携システム 案件詳細_files/jquery-2.2.0.min.js.ダウンロード"></script>
  
  <script src="./売却物件情報連携システム 案件詳細_files/control.js.ダウンロード"></script>
  
  <script src="./売却物件情報連携システム 案件詳細_files/jquery-ui.min.js.ダウンロード"></script>
    
  <script src="./売却物件情報連携システム 案件詳細_files/jquery.ui.datepicker-ja.min.js.ダウンロード"></script>
  

<script type="text/javascript">
<!--

// fileTagの有効・無効一括変更
function changeFileTag(flag){
    $("#uploadFile01").prop("disabled", flag);
    $("#uploadFile02").prop("disabled", flag);
    $("#uploadFile03").prop("disabled", flag);
    $("#uploadFile04").prop("disabled", flag);
    $("#uploadFile05").prop("disabled", flag);
    $("#uploadFile06").prop("disabled", flag);
}

//code3桁の入力チェック
function checkCode3(objId, name) {
    if($(objId).val() == "") {
        alert(name + 'が入力されていません');
        $(objId).focus();
        return false;
    }
    if($(objId).val().length != 3) {
        alert(name + 'は３桁で入力してください');
        $(objId).focus();
        return false;
    }
    if (!$(objId).val().match(/^[0-9]*$/)) {
      alert(name + 'は半角数値で入力してください');
      $(objId).focus();
      return false;
    }
    return true;
}
  
//code6桁の入力チェック
function checkCode6(objId, name) {
    if($(objId).val() == "") {
        alert(name + 'が入力されていません');
        $(objId).focus();
        return false;
    }
    if($(objId).val().length != 6) {
        alert(name + 'は６桁で入力してください');
        $(objId).focus();
        return false;
    }
    if (!$(objId).val().match(/^[0-9]*$/)) {
      alert(name + 'は半角数値で入力してください');
      $(objId).focus();
      return false;
    }
    return true;
}
  
//日付の入力チェック
function checkInputDate(objId, name) {
    if($(objId).val() == "" || $(objId).val() == "0") {
        // ブランク、0は許容
        return true;
    }
    if($(objId).val().length != 8 || !$(objId).val().match(/^[0-9]*$/)) {
        alert(name + 'は８桁の半角数値で入力してください\n例：2024年3月10日→20240310\n日付を削除したい場合は0を入力してください');
        $(objId).focus();
        return false;
    }
    var today = new Date();
    var yyyy = today.getFullYear();
    var mm = ('0' + (today.getMonth()+1)).slice(-2);
    var dd = ('0' + today.getDate()).slice(-2);
    var date = yyyy + mm +dd;
    if($(objId).val() > date ){
        alert(name + 'は過去の日付で入力してください\n日付を削除したい場合は0を入力してください');
        $(objId).focus();
        return false;
    }
    return true;
}
//日付の入力チェック(引渡し予定日と合意解約予定日)
function checkInputDateFuture(objId, orgObjId, name) {
    if($(objId).val() == "" || $(objId).val() == "0") {
        // ブランク、0は許容
        return true;
    }
    if($(objId).val() == $(orgObjId).val()) {
        // 値を更新していない場合はチェック不要
        return true;
    }
    if($(objId).val().length != 8 || !$(objId).val().match(/^[0-9]*$/)) {
        alert(name + 'は８桁の半角数値で入力してください\n例：2024年3月10日→20240310\n日付を削除したい場合は0を入力してください');
        $(objId).focus();
        return false;
        }
    var today = new Date();
    var yyyy = today.getFullYear();
    var mm = ('0' + (today.getMonth()+1)).slice(-2);
    var dd = ('0' + today.getDate()).slice(-2);
    var date = yyyy + mm +dd;
    if($(objId).val() <= date ){
        alert(name + 'は未来の日付で入力してください(当日不可)\n日付を削除したい場合は0を入力してください');
        $(objId).focus();
        return false;
    }
    return true;
}

//URLの入力チェック
function checkURL(objId, name) {
    if($(objId).val() == "") {
        // ブランクは許容
        return true;
    }
    if($(objId).val().length > 255) {
        alert('URLは255文字以下で入力してください');
    }
    try {
        new URL($(objId).val());
        return true;
    } catch (err) {
        alert('URLを入力してください');
        $(objId).focus();
        return false;
    }
}
  
//コメントの入力チェック
function checkCommentDate(objId, name) {
    if($(objId).val() == "") {
        // ブランクは許容
        return true;
    }
    // 改行コードは全角変換してチェック
    var tmpStr = $(objId).val().replaceAll("\n", "￥ｎ");

    // エスケープ処理
    var input = htmlspecialchars(tmpStr);

    if(input.length > 300) {
        alert('コメントは３００文字以下で入力してください');
        return false;
    }
    return true;
}

//エスケープ処理
function htmlspecialchars(unsafeText){
    if(typeof unsafeText !== 'string'){
      return unsafeText;
    }
    return unsafeText.replace(
      /[&'`"<>]/g, 
      function(match) {
        return {
          '&': '&amp;',
          "'": '&apos;',
          '/': '&#x2F;',
          '"': '&quot;',
          '<': '&lt;',
          '>': '&gt;',
        }[match]
      }
    );
  }

// 入力チェック
function checkInputData() {
    // 店舗コード
    if (!checkCode3("#stoCd", "店舗コード")) {
        return false;
    }
    // リーシング担当者
    if (!checkCode6("#stoTtm", "リーシング担当者")) {
        return false;
    }
    // 営業所コード
    if (!checkCode3("#egsCd", "営業所コード")) {
        return false;
    }
    // パートナーズ担当者
    if (!checkCode6("#ptnsTtm", "パートナーズ担当者")) {
      return false;
    }
    // 問題案件DBリンク
    if (!checkURL("#mndAnknLink", "問題案件DBリンク")) {
      return false;
    }

    // 訪問説明完了日
    if (!checkInputDate("#ownShdkEnd", "訪問説明完了日")) {
      return false;
    }
    // レントロール確認日
    if (!checkInputDate("#fomRvd", "レントロール確認日")) {
      return false;
    }
    // 継承説明日
    if (!checkInputDate("#shkeiDscD", "継承説明日")) {
      return false;
    }
    // 売買契約締結日
    if (!checkInputDate("#bkyTkdInp", "売買契約締結日")) {
      return false;
    }
    // 引渡し予定日
    if (!checkInputDateFuture("#hkwEndYtd", "#orgHkwEndYtd", "引渡し予定日")) {
      return false;
    }
    
    // 引渡し日
    if (!checkInputDate("#hkwEnd", "引渡し日")) {
      return false;
    }
    // 確認日
    if (!checkInputDate("#shEnd", "確認日")) {
      return false;
    }
    // コメント
    if (!checkCommentDate("#comment", "コメント")) {
      return false;
    }
    return true;
}

// 案件一覧画面へ戻る
function backAnkenListDisp() {
    if(!confirm('案件一覧へ戻りますが、よろしいですか？')){
        /* キャンセルの時の処理 */
        return false;
    }
    window.location.href = '売却物件情報連携システム 案件一覧-data.html';
    return true;
}

$(function() {
    // 更新ボタン押下時の確認メッセージ
    $('#updateBtn').click(function(e) {
        if (!checkInputData()) {
            return false;
        }
        if(!confirm('更新してよろしいですか？')){
            /* キャンセルの時の処理 */
            return false;
        }
        changeFileTag(true);
        $('form').attr('action', '/BaibaiJohoKanri/detail/updateBtn');
        $('form').submit();
        return true;
    });
    
    // キャンセルボタン押下時の確認メッセージ
    $('#cancelBtn').click(function(e) {
        if(!confirm('案件をキャンセルしてよろしいですか？')){
            /* キャンセルの時の処理 */
            return false;
        }
        if($('#comment').val() == "") {
            alert('キャンセル時はコメントも登録してください');
            $('#comment').focus();
            return false;
        }
        // コメントのチェック
        if (!checkCommentDate("#comment", "コメント")) {
          return false;
        }
        changeFileTag(true);
        $('form').attr('action', '/BaibaiJohoKanri/detail/cancelBtn');
        $('form').submit();
        return true;
    });

    // 案件一覧へ戻るボタン押下時の確認メッセージ
    $('#backBtn').click(function(e) {
        return backAnkenListDisp();
    });
    $('#backBtn2').click(function(e) {
        return backAnkenListDisp();
    });

});

function checkFile(subDir){
    id = '#uploadFile' + subDir;
    field = $(id).prop('files')[0];
    if (!field) {
        alert('ファイルが選択されていません');
        return false;
    }
    var fileSize = $(id).prop('files')[0].size;
    if (field.size > (10 * 1024 * 1024)) {
        alert('添付できるファイルサイズは10MB未満です');
        return false;
    }
    return true;
}

// 自社物件受付時チェックシートのファイル添付ボタン押下時の確認メッセージ
function uploadFiles(subDir){
    if(!confirm('ファイルを添付してよろしいですか？')){
        /* キャンセルの時の処理 */
        return false;
    }
    if(!checkFile(subDir)){
        return false;
    }

    if (subDir != '01') {
        $("#uploadFile01").prop("disabled", true);
    }
    if (subDir != '02') {
        $("#uploadFile02").prop("disabled", true);
    }
    if (subDir != '03') {
        $("#uploadFile03").prop("disabled", true);
    }
    if (subDir != '04') {
        $("#uploadFile04").prop("disabled", true);
    }
    if (subDir != '05') {
        $("#uploadFile05").prop("disabled", true);
    }
    if (subDir != '06') {
        $("#uploadFile06").prop("disabled", true);
    }

    var url = '/BaibaiJohoKanri/detail/upload?subDir=' + subDir;
    $('form').attr('action', url);
    $('form').attr('enctype', 'multipart/form-data');
    $('form').submit();

    return true;
}

function downloadFile(fileName, subDir){
    if(!confirm("「" + fileName +"」をダウンロードしますか？")){
        /* キャンセルの時の処理 */
        return false;
    }
    // Post前、fileTagを無効化
    changeFileTag(true);

    var url = '/BaibaiJohoKanri/detail/download?fileName=' + fileName + '&subDir=' + subDir;
    $('form').attr('action', url);
    $('form').submit();

    // Post後、にfileTagを有効化
    changeFileTag(false);
    return false;
}

function deleteFile(fileName, subDir){
    if(!confirm("貼付の「" + fileName +"」を削除してよろしいですか？")){
        /* キャンセルの時の処理 */
        return false;
    }
    // Post前、fileTagを無効化
    changeFileTag(true);

    var url = '/BaibaiJohoKanri/detail/delete?fileName=' + fileName + '&subDir=' + subDir;
    $('form').attr('action', url);
    $('form').submit();
    return false;
}

function requestPartners(newSts, newStsDtl, message){
    if(!confirm(message + 'してよろしいですか？')){
        /* キャンセルの時の処理 */
        return false;
    }
    changeFileTag(true);
    if ($('#sts').val() < newSts) {
        // ステータスは戻さない
        $('#sts').val(newSts);
    }
    $('#stsDtl').val(newStsDtl);
    $('form').attr('action', '/BaibaiJohoKanri/detail/sendMailToPartners');
    $('form').submit();
    return true;
}

function requestLeasing(newSts, newStsDtl, message){
    if(!confirm(message + 'してよろしいですか？')){
        /* キャンセルの時の処理 */
        return false;
    }
    changeFileTag(true);
    if ($('#sts').val() < newSts) {
        // ステータスは戻さない
        $('#sts').val(newSts);
    }
    $('#stsDtl').val(newStsDtl);
    $('form').attr('action', '/BaibaiJohoKanri/detail/sendMailToLeasing');
    $('form').submit();
    return true;
}

function showHide(divId, imgId){
    if ($("#" + divId).is(':hidden')) {
        // 非表示の場合の処理
        $("#"+divId).show();
        $("#"+imgId).attr('src', '/BaibaiJohoKanri/img/icon_minus.png');
    } else {
        // 表示されている場合の処理
        $("#"+divId).hide();
        $("#"+imgId).attr('src', '/BaibaiJohoKanri/img/icon_plus.png');
    }
}

// リンクの表示ボタン押下時の処理
function showLinkDisplay(){
    // Excelファイルをダウンロード
    const link = document.createElement('a');
    link.href = 'download/040_自社物件売却受付時チェックシート.xlsx';
    link.download = '040_自社物件売却受付時チェックシート.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

$(function(){

});

$(function() {
    contextRoot = '/BaibaiJohoKanri';
    // ----------------------コード定義----------------------------------------------------
    codeAssociation = {
        // パートナーズ営業所コード
        'egsNm' : {
            'codeId' : 'office',
            'mapping' : {
                'jgsKb' : {
                    itemId : 'egsJgsKb',
                    key : true
                },
                'jscd6Kt' : {
                    itemId : 'egsCd',
                    key : true
                },
                'jgsNm' : {
                    itemId : 'egsNm'
                }
            }
        },
        // パートナーズ担当者コード
        'ptnsTtmNm' : {
            'codeId' : 'employee',
            'mapping' : {
                'empNo' : {
                    itemId : 'ptnsTtm',
                    key : true
                },
                'empNm' : {
                    itemId : 'ptnsTtmNm'
                }
            }
        },
        // リーシング店舗コード
        'stoNm' : {
            'codeId' : 'office',
            'mapping' : {
                'jgsKb' : {
                    itemId : 'stoJgsKb',
                    key : true
                },
                'jscd6Kt' : {
                    itemId : 'stoCd',
                    key : true
                },
                'jgsNm' : {
                    itemId : 'stoNm'
                }
            }
        },
        // リーシング担当者コード
        'stotmNm' : {
            'codeId' : 'employee',
            'mapping' : {
                'empNo' : {
                    itemId : 'stoTtm',
                    key : true
                },
                'empNm' : {
                    itemId : 'stotmNm'
                }
            }
        }
    }
});

$(function() {
    
    $.datepicker.setDefaults({
        dateFormat: "yymmdd",
        buttonImage: "img/cal.png",    // カレンダーアイコン画像
        buttonImageOnly: true,         // 画像として表示
        showOn: "button",              // カレンダー呼び出し元の定義
        buttonText: "カレンダーから選択"
    });

    // 訪問説明完了日(未来日選択不可)
    $( '#ownShdkEnd' ).datepicker({
        beforeShowDay: function(date) {
          var now = new Date();
          var dateStr = createDateStr(date);
          
          if (date.getTime() > now.getTime()) {
            return [false];
          }else {
            return [true];
          }
        },
        disabled: activeCheck("ownShdkEnd")
      });
    
    // レントロール確認日(未来日選択不可)
    $( '#fomRvd' ).datepicker({
        beforeShowDay: function(date) {
          var now = new Date();
          var dateStr = createDateStr(date);
          
          if (date.getTime() > now.getTime()) {
            return [false];
          }else {
            return [true];
          }
        },
      disabled: activeCheck("fomRvd")
      });
    
    // 継承説明日(未来日選択不可)
    $( '#shkeiDscD' ).datepicker({
        beforeShowDay: function(date) {
          var now = new Date();
          var dateStr = createDateStr(date);
          
          if (date.getTime() > now.getTime()) {
            return [false];
          }else {
            return [true];
          }
        },
      disabled: activeCheck("shkeiDscD")
      });
    
    // 売買契約締結日(未来日選択不可)
    $( '#bkyTkdInp' ).datepicker({
        beforeShowDay: function(date) {
          var now = new Date();
          var dateStr = createDateStr(date);
          
          if (date.getTime() > now.getTime()) {
            return [false];
          }else {
            return [true];
          }
        },
      disabled: activeCheck("bkyTkdInp")
      });
    
    // 引渡し予定日(過去日選択不可)
    $( '#hkwEndYtd' ).datepicker({
          beforeShowDay: function(date) {
              var now = new Date();
              var dateStr = createDateStr(date);
              
              if (date.getTime() < now.getTime()) {
                return [false];
              }else {
                return [true];
              }
            },
            disabled: activeCheck("hkwEndYtd")
          });

    //一括賃貸借/管理委託契約の締結日(未来日選択不可)
    $( '#tkdInp' ).datepicker({
        beforeShowDay: function(date) {
          var now = new Date();
          var dateStr = createDateStr(date);
          
          if (date.getTime() > now.getTime()) {
            return [false];
          }else {
            return [true];
          }
        },
      disabled: activeCheck("tkdInp")
      });

 // 合意解約予定日(過去日選択不可)
    $( '#gcnlD' ).datepicker({
          beforeShowDay: function(date) {
              var now = new Date();
              var dateStr = createDateStr(date);
              
              if (date.getTime() < now.getTime()) {
                return [false];
              }else {
                return [true];
              }
            },
            disabled: activeCheck("gcnlD")
          });
    
    // 引渡し日(未来日選択不可)
    $( '#hkwEnd' ).datepicker({
        beforeShowDay: function(date) {
          var now = new Date();
          var dateStr = createDateStr(date);
          
          if (date.getTime() > now.getTime()) {
            return [false];
          }else {
            return [true];
          }
        },
      disabled: activeCheck("hkwEnd")
      });
    
    // 確認日(未来日選択不可)
    $( '#shEnd' ).datepicker({
        beforeShowDay: function(date) {
          var now = new Date();
          var dateStr = createDateStr(date);
          
          if (date.getTime() > now.getTime()) {
            return [false];
          }else {
            return [true];
          }
        },
      disabled: activeCheck("shEnd")
      });
    });

    function createDateStr(date) {
        var year = date.getYear();
        var month = date.getMonth() + 1;
        var day = date.getDate();
        if (year < 2000) { year += 1900 };
        if (month < 10)  { month = "0" + month };
        if (day < 10)    { day = "0" + day };
        
        var dateStr = year + month + day;
        return dateStr;
      }
    
    function activeCheck(day){
        var sts = 1
        var cmpCd = 001
        var flg = true
        
        if((day == "ownShdkEnd")){
            if(cmpCd=='160'){
                return true;
            }
        }else if((day == "fomRvd") || (day == "shkeiDscD")){
            if((flg) && (cmpCd=='001'|| cmpCd=='018')){
                return false;
            }else if(sts == 1 || cmpCd=='160'){
                return true;
                }
            return false;
            }else if ((day == "bkyTkdInp") || (day == "hkwEndYtd") ){
                if((flg) && (cmpCd=='001'|| cmpCd=='160')){
                    return false;
                }else if(sts == 1 || sts == 2 || cmpCd=='018'){
                    return true;
                    }
                return false;
            }else if ((day == "tkdInp") || (day == "gcnlD")){
                if((flg) && (cmpCd=='001'|| cmpCd=='018')){
                    return false;
                }else if(sts == 1 || sts == 2 || cmpCd=='160'){
                    return true;
                    }
                return false;
        }else if (day == "hkwEnd"){
            if((flg) && (cmpCd=='001'|| cmpCd=='160')){
                return false;
            }else if(sts == 1 || sts == 2 || sts == 3 || cmpCd=='018'){
                return true;
                }
            return false;
        }else if (day == "shEnd"){
            if((flg) && (cmpCd=='001'|| cmpCd=='018')){
                return false;
            }else if(sts == 1 || sts == 2 || sts == 3 || sts == 4 || cmpCd=='160'){
                return true;
                }
            return false;
        }
    }

//-->
</script>
</head>
<body>
  <h2 align="center">売却物件情報連携システム</h2>

  <div class="container">
  <div class="contents">
    
    <div style="display: flex;">
      <p>【売却物件の詳細です】</p>
      <p style="margin-left:585px;">
        <a href="http://snabptlapv1.kentaku.co.jp/aqua/492567B6002DAF47-B42F4A152F1AB8F2492585F9003ACA95/view" target="_blank">パートナーズ管理業務掲示板リンク</a><br>
        <a href="http://snabptlapv1.kentaku.co.jp/aqua/59c407dc-15f4-4156-8149-a95825a13f64/view?manual-in=e834be7d-4d8a-4576-bf96-6c447656f4e1" target="_blank">リーシング業務マニュアルリンク</a>
      </p>
    </div>
    <form id="logout" action="http://devap9001/BaibaiJohoKanri/detail/updateBtn" method="POST">
    <input type="hidden" name="_csrf" value="fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1">
    <div class="header">
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
          </tr>
          <tr>
            <td colspan="3">案件No</td>
            <td colspan="7">
              <input type="text" name="ankenNo" class="output-code-text" value="000000371" readonly="">
            </td>
          </tr>
          <tr>
            <td colspan="3">ステータス</td>
            <td>
              <input type="text" name="ankenDetailList[0].stsNm" id="stsNm" class="output-text" value="売却相談中" readonly="">
              <input type="hidden" name="ankenDetailList[0].sts" id="sts" value="1">
            </td>
            <td colspan="4">
              <input type="text" name="ankenDetailList[0].stsDtl" id="stsDtl" class="output-text" value="要実施者：パートナーズ                  " readonly="">
            </td>
            <td align="right" colspan="2">
              <button type="button" id="backBtn" class="detail-button">案件一覧へ戻る</button>
            </td>
          </tr>
          <tr>
            <td colspan="3">リーシング店舗</td>
            <td>
              <input type="text" name="stoCd" id="stoCd" class="input-code-text" value="216" maxlength="3">
              <input type="hidden" name="ankenDetailList[0].stoCd" id="hiddenStoCd" value="216">
            </td>
            <td colspan="7">
              <input type="text" name="stoNm" id="stoNm" class="output-text" value="白石駅前　　　　　　　　　　　　　　　　" readonly="">
            </td>
          </tr>
          <tr>
            <td colspan="3">リーシング担当者</td>
            <td>
              <input type="text" name="stoTtm" id="stoTtm" class="input-code-text" value="053205" maxlength="6">
              <input type="hidden" name="ankenDetailList[0].lsgTtm" id="hiddenLsgTtm" value="053205">
            </td>
            <td colspan="6">
              <input type="text" name="stotmNm" id="stotmNm" class="output-text" value="吉あ　ああ　　　　　　　" readonly="">
            </td>
          </tr>
          <tr>
            <td colspan="3">管理営業所</td>
            <td>
              <input type="text" name="egsCd" id="egsCd" class="input-code-text" value="853" maxlength="3">
            </td>
            <td colspan="6">
              <input type="text" name="egsNm" id="egsNm" class="output-text" value="板橋　　　　　　　　　　　　　　　　　　" readonly="">
            </td>
          </tr>
          <tr>
            <td colspan="3">パートナーズ担当者</td>
            <td>
              <input type="text" name="ptnsTtm" id="ptnsTtm" class="input-code-text" value="014994" maxlength="6">
            </td>
            <td colspan="6">
              <input type="text" name="ptnsTtmNm" id="ptnsTtmNm" class="output-text" value="斉あ　ああ　　　　　　　" readonly="">
            </td>
          </tr>
          <input type="hidden" name="egsJgsKb" id="egsJgsKb" value="F">
          <input type="hidden" name="stoJgsKb" id="stoJgsKb" value="E">
      
          <tr>
            <td colspan="3">建物情報</td>
            <td>
              <input type="text" name="ankenDetailList[0].bldCd" class="output-code-text" value="013216401" readonly="">
            </td>
            <td colspan="7">
              <input type="text" name="ankenDetailList[0].bldNm" class="output-text" value="マンシ○ンリ○ー○イ○" readonly="">
            </td>
          </tr>
      
          <tr>
            <td colspan="3">問題案件DBリンク</td>
            <td colspan="5">
              <input type="text" name="ankenDetailList[0].mndAnknLink" id="mndAnknLink" maxlength="255" value="" style="width: 600px">
            </td>
            <td align="right" colspan="2">
              
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <hr>
    <span><b>売却相談中</b><div align="right"><img src="./売却物件情報連携システム 案件詳細_files/icon_minus.png" id="showHide1" onclick="showHide(&quot;status1&quot;, &quot;showHide1&quot;)"></div></span>
    <div id="status1">
      
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <td class="width10"></td><td class="width10"></td><td class="width10"></td><td class="width10"></td><td class="width10"></td>
            <td class="width10"></td><td class="width10"></td><td class="width10"></td><td class="width10"></td><td class="width10"></td>
          </tr>
          <tr>
            <td colspan="10"><b>【依頼】自社物件受付時チェックシート（実施者：リーシング）</b></td>
          </tr>
          <tr>
            <td colspan="10">下記の自社物件受付時チェックシートを「ファイル選択」から選択。「ファイル添付」で更新の上「パートナーズへ依頼」を押下</td>
          </tr>
          <tr>
            <td colspan="10">
              <a href="http://snabptlapv1.kentaku.co.jp/aqua/c73d93b8-a424-449c-85f2-01cd0d6cbfa4/view" target="_blank">自社物件売却受付時チェックシートリンク</a>
              <button type="button" onclick="window.open('自社物件売却受付時チェックシート.html', '_blank')" class="detail-button" style="margin-left: 10px;">入力画面へ</button>
              <input type="checkbox" id="checksheet_completed" name="checksheet_completed" style="margin-left: 10px;">
              <label for="checksheet_completed" style="margin-left: 5px;"></label>
              <button type="button" onclick="showLinkDisplay()" class="detail-button" style="margin-left: 10px;">
                <img src="icon/211853_link_icon.svg" alt="link" style="width: 16px; height: 16px; vertical-align: middle; margin-right: 5px;">
                リンクの表示
              </button>
            </td>
          </tr>
          <tr>
            <td colspan="10"><a href="http://snabptlapv1.kentaku.co.jp/aqua/743ef734-9e12-499e-ae1a-a272ca9b0d96/view" target="_blank">ガスパルチェックシートリンク</a></td>
          </tr>
          <tr>
            <td colspan="9">
              <input type="file" id="uploadFile01" name="uploadFile">
            </td>
            <td align="right">
              <input type="button" onclick="uploadFiles(&quot;01&quot;)" name="uploadFile" class="detail-button" value="ファイル添付">
            </td>
          </tr>
      
          <tr>
            <td colspan="9">　</td>
            <td align="right">
              <input type="button" id="request_partners" onclick="requestPartners(&quot;1&quot;, &quot;要実施者：パートナーズ&quot;, &quot;パートナーズへ依頼&quot;)" class="detail-button" value="パートナーズへ依頼">
            </td>
          </tr>
          <tr>
            <td colspan="10">　</td>
          </tr>
          <tr>
            <td colspan="10"><b>【回答】自社物件受付時チェックシート（実施者：パートナーズ）</b></td>
          </tr>
          <tr>
            <td colspan="10">①確認済みの自社物件受付時チェックシートを「ファイルを選択」から選択し「ファイル添付」を押下</td>
          </tr>
          <tr>
            <td colspan="10">②下記「１．～７」書類を「ファイルを選択」から選択し「ファイル添付」を押下</td>
          </tr>
          <tr>
            <td colspan="10">　&lt;システム添付資料&gt;</td>
          </tr>
          <tr>
            <td colspan="10">　　１．合意解約時賃貸人契約内容一覧表、２．追加営繕工事履歴一覧表、３．合意解約ご確認書（未署名）４．新特約解約金算出ツール、</td>
          </tr>
          <tr>
            <td colspan="10">　　５．一括賃貸借契約書または管理委託契約書、６．賃貸収入ご報告書、７．クレームWEBシステムの画面印刷</td>
          </tr>
          <tr>
            <td colspan="10">③ガス会社を選択し、画面最下部の「更新」を押下</td>
          </tr>
          <tr>
            <td colspan="10">④「リーシングへ回答」を押下</td>
          </tr>
          <tr>
            <td colspan="9">
              <input type="file" id="uploadFile02" name="uploadFile">
            </td>
            <td align="right">
              <input type="button" onclick="uploadFiles(&quot;02&quot;)" name="uploadFile" class="detail-button" value="ファイル添付">
            </td>
          </tr>
      
          <tr>
            <td colspan="10">
              <a href="javascript:downloadFile(&quot;文字化けテストmoji1234.xlsx&quot;, &quot;02&quot;)"><img src="./売却物件情報連携システム 案件詳細_files/file_icon.png" alt="文字化けテストmoji1234.xlsx"></a>文字化けテストmoji1234.xlsx
              <a href="javascript:deleteFile(&quot;文字化けテストmoji1234.xlsx&quot;, &quot;02&quot;)"><img src="./売却物件情報連携システム 案件詳細_files/del_icon.png" alt="文字化けテストmoji1234.xlsx"></a>
            </td>
          </tr>
      
          <tr>
            <td colspan="3">ガス会社</td>
            <td colspan="7">
              <input type="radio" id="isGaspal01" name="ankenDetailList[0].gspCkstYo" value="1">
              <label for="isGaspal01">ガスパル</label>
              <input type="radio" id="isGaspal02" name="ankenDetailList[0].gspCkstYo" value="2">
              <label for="isGaspal02">それ以外</label>
            </td>
          </tr>
          <tr>
            <td colspan="9">　</td>
            <td align="right">
              <input type="button" id="request_leasing" onclick="requestLeasing(&#39;1&#39;, &#39;要実施者：パートナーズ&#39;, &#39;リーシングへ回答&#39;)" class="detail-button" value="リーシングへ回答">
            </td>
          </tr>
          <tr>
            <td colspan="10">　</td>
          </tr>
          <tr>
            <td colspan="10"><b>【報告】合意解約ご確認書オーナー様への訪問説明完了日（実施者：パートナーズ）</b></td>
          </tr>
          <tr>
            <td colspan="10">①オーナー様署名済みの合意解約ご確認書を「ファイル選択」から選択し「ファイル添付」を押下</td>
          </tr>
          <tr>
            <td colspan="10">②訪問説明完了日を入力し、画面最下部の「更新」を押下</td>
          </tr>
          <tr>
            <td colspan="10">③「リーシング報告」を押下</td>
          </tr>
          <tr>
            <td colspan="9">
              <input type="file" id="uploadFile03" name="uploadFile">
            </td>
            <td align="right">
              <input type="button" onclick="uploadFiles(&quot;03&quot;)" name="uploadFile" class="detail-button" value="ファイル添付">
            </td>
          </tr>
      
          <tr>
            <td colspan="3">訪問説明完了日</td>
            <td colspan="7">
              <input type="text" name="ankenDetailList[0].ownShdkEnd" id="ownShdkEnd" class="input-code-text hasDatepicker" maxlength="8" value=""><img class="ui-datepicker-trigger" src="./売却物件情報連携システム 案件詳細_files/cal.png" alt="カレンダーから選択" title="カレンダーから選択">
          </td>
          </tr>
          <tr>
            <td colspan="9">　</td>
            <td align="right">
              <input type="button" id="request_leasing" onclick="requestLeasing(&#39;2&#39;, &#39;要実施者：リーシング&#39;, &#39;リーシングへ報告&#39;)" class="detail-button" value="リーシングへ報告">
            </td>
          </tr>
          <tr>
            <td colspan="10">　</td>
          </tr>
        </tbody>
      </table>
    </div>
    <hr>
    <span><b>販売中</b><div align="right"><img src="./売却物件情報連携システム 案件詳細_files/icon_minus.png" id="showHide2" onclick="showHide(&quot;status2&quot;, &quot;showHide2&quot;)"></div></span>
    <div id="status2">
      
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <td class="width10"></td><td class="width10"></td><td class="width10"></td><td class="width10"></td><td class="width10"></td>
            <td class="width10"></td><td class="width10"></td><td class="width10"></td><td class="width10"></td><td class="width10"></td>
          </tr>
          <tr>
            <td colspan="10"><b>【依頼】レントロール（実施者：リーシング）</b></td>
          </tr>
          <tr>
            <td colspan="10">「確認依頼画面」でデータ取込・保存を行い、「パートナーズへ依頼」を押下</td>
          </tr>
      
            <tr>
              <td colspan="2">案件No</td>
              <td colspan="2">
                <input type="text" class="output-code-text" value="000000371" readonly="">
              </td>
              <td colspan="2">
                <input type="text" class="output-text" value="" readonly="">
              </td>
              <td colspan="3"></td>
              <td align="right">
                <button type="button" class="detail-button" onclick="location.href=&#39;/BaibaiJohoKanri/rentroll?ankenNo=000000371&amp;bldCd=013216401&amp;bldNm=マンシ○ンリ○ー○イ○&amp;_csrf=fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1&amp;stoNm=白石駅前　　　　　　　　　　　　　　　　&amp;stotmNm=吉あ　ああ　　　　　　　&amp;fomRvd=20240724&#39;">確認依頼画面へ</button>
              </td>
            </tr>
          
          <tr>
            <td colspan="2">建物情報</td>
            <td colspan="2">
              <input type="text" class="output-code-text" value="013216401" readonly="">
            </td>
            <td colspan="2">
              <input type="text" class="output-text" value="マンシ○ンリ○ー○イ○" readonly="">
            </td>
          </tr>
      
          <tr>
            <td colspan="9">　</td>
            <td align="right">
              <input type="button" id="request_partners" onclick="requestPartners(&quot;2&quot;, &quot;要実施者：パートナーズ&quot;, &quot;パートナーズへ依頼&quot;)" class="detail-button" value="パートナーズへ依頼">
            </td>
          </tr>
          <tr>
            <td colspan="10">　</td>
          </tr>
          <tr>
            <td colspan="10"><b>【確認】レントロール（実施者：パートナーズ）</b></td>
          </tr>
          <tr>
            <td colspan="10">①「確認画面」を押下し、レントロールを確認。必要があれば修正し「保存」を押下</td>
          </tr>
          <tr>
            <td colspan="10">　※最新の賃料と一致しているか、確認ください。</td>
          </tr>
          <tr>
            <td colspan="10">　　データ確認先：DK-WORKS　家賃台帳照会（家賃明細・自治会費等）、賃貸収入ご報告書</td>
          </tr>
          <tr>
            <td colspan="10">②確認完了後、「レントロール確認日」を入力し、画面最下部の「更新」を押下</td>
          </tr>
          <tr>
            <td colspan="10">③「リーシングへ回答」を押下</td>
          </tr>
      
            <tr>
              <td colspan="2">案件No</td>
              <td colspan="2">
                <input type="text" class="output-code-text" value="000000371" readonly="">
              </td>
              <td colspan="2">
                <input type="text" class="output-text" value="" readonly="">
              </td>
              <td colspan="3"></td>
              <td align="right">
                <button type="button" class="detail-button" onclick="location.href=&#39;/BaibaiJohoKanri/rentroll?ankenNo=000000371&amp;bldCd=013216401&amp;bldNm=マンシ○ンリ○ー○イ○&amp;_csrf=fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1&amp;stoNm=白石駅前　　　　　　　　　　　　　　　　&amp;stotmNm=吉あ　ああ　　　　　　　&amp;fomRvd=20240724&#39;">確認画面へ</button>
              </td>
            </tr>
          
          <tr>
            <td colspan="2">建物情報</td>
            <td colspan="2">
              <input type="text" class="output-code-text" value="013216401" readonly="">
            </td>
            <td colspan="2">
              <input type="text" class="output-text" value="マンシ○ンリ○ー○イ○" readonly="">
            </td>
            <td colspan="3"></td>
          </tr>
      
          <tr>
            <td colspan="3">レントロール確認日</td>
            <td colspan="6">
              <input type="text" name="ankenDetailList[0].fomRvd" id="fomRvd" class="input-code-text hasDatepicker" maxlength="8" value="20240724"><img class="ui-datepicker-trigger" src="./売却物件情報連携システム 案件詳細_files/cal.png" alt="カレンダーから選択" title="カレンダーから選択">
            </td>
            <td align="right">
              <input type="button" id="request_leasing" onclick="requestLeasing(&#39;2&#39;, &#39;要実施者：リーシング&#39;, &#39;リーシングへ回答&#39;)" class="detail-button" value="リーシングへ回答">
            </td>
          </tr>
          <tr>
            <td colspan="10">　</td>
          </tr>
          <tr>
            <td colspan="10"><b>【出力】レントロール（実施者：リーシング）</b></td>
          </tr>
          <tr>
            <td colspan="10">①【依頼】レントロール（実施者：リーシング）の確認依頼画面を押下</td>
          </tr>
          <tr>
            <td colspan="10">②画面内の「PDF出力」を押下。（パートナーズ確認済みのレントロール）</td>
          </tr>
          <tr>
            <td colspan="10">③出力したPDFファイルを「ファイル選択」から選択「ファイル添付」で更新</td>
          </tr>
          <tr>
            <td colspan="9">
              <input type="file" id="uploadFile04" name="uploadFile">
            </td>
            <td align="right">
              <input type="button" onclick="uploadFiles(&quot;04&quot;)" name="uploadFile" class="detail-button" value="ファイル添付">
            </td>
          </tr>
      
          <tr>
            <td colspan="10">　</td>
          </tr>
          <tr>
            <td colspan="10"><b>【依頼】借上継承/管理継承の説明（実施者：リーシング）</b></td>
          </tr>
          <tr>
            <td colspan="9">買主への継承説明を依頼</td>
            <td align="right">
              <input type="button" id="request_partners" onclick="requestPartners(&quot;2&quot;, &quot;要実施者：パートナーズ&quot;, &quot;パートナーズへ依頼&quot;)" class="detail-button" value="パートナーズへ依頼">
            </td>
          </tr>
          <tr>
            <td colspan="10">　</td>
          </tr>
          <tr>
            <td colspan="10"><b>【完了】借上継承/管理継承の説明（実施者：パートナーズ）</b></td>
          </tr>
          <tr>
            <td colspan="10">①買主様への説明完了後、「継承説明日」を入力し、画面最下部の「更新」を押下</td>
          </tr>
          <tr>
            <td colspan="10">②「リーシングへ報告」を押下</td>
          </tr>
          <tr>
            <td colspan="3">継承説明日</td>
            <td colspan="6">
              <input type="text" name="ankenDetailList[0].shkeiDscD" id="shkeiDscD" class="input-code-text hasDatepicker" maxlength="8" value=""><img class="ui-datepicker-trigger" src="./売却物件情報連携システム 案件詳細_files/cal.png" alt="カレンダーから選択" title="カレンダーから選択">
            </td>
            <td align="right">
              <input type="button" id="request_leasing" onclick="requestLeasing(&#39;3&#39;, &#39;要実施者：リーシング&#39;, &#39;リーシングへ報告&#39;)" class="detail-button" value="リーシングへ報告">
            </td>
          </tr>
          <tr>
            <td colspan="10">　</td>
          </tr>
        </tbody>
      </table>
    </div>
    <hr>
    <span><b>契約済み</b><div align="right"><img src="./売却物件情報連携システム 案件詳細_files/icon_minus.png" id="showHide3" onclick="showHide(&quot;status3&quot;, &quot;showHide3&quot;)"></div></span>
    <div id="status3">
      
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
          </tr>
          <tr>
            <td colspan="10"><b>【報告】売買契約内容（実施者：リーシング）</b></td>
          </tr>
          <tr>
            <td colspan="10">①売買契約書を「ファイル選択」から選択し、「ファイル添付」を押下</td>
          </tr>
          <tr>
            <td colspan="10">②売買契約締結日・引渡し予定日・売買後管理形態を入力し、画面最下部の「更新」を押下</td>
          </tr>
          <tr>
            <td colspan="10">③「パートナーズへ報告」を押下</td>
          </tr>
          <tr>
            <td colspan="9">
              <input type="file" id="uploadFile05" name="uploadFile">
            </td>
            <td align="right">
              <input type="button" onclick="uploadFiles(&quot;05&quot;)" name="uploadFile" class="detail-button" value="ファイル添付">
            </td>
          </tr>
      
          <tr>
            <td colspan="3">売買契約締結日</td>
            <td colspan="7">
              <input type="text" name="ankenDetailList[0].bkyTkdInp" id="bkyTkdInp" class="input-code-text hasDatepicker" maxlength="8" value=""><img class="ui-datepicker-trigger" src="./売却物件情報連携システム 案件詳細_files/cal.png" alt="カレンダーから選択" title="カレンダーから選択">
            </td>
          </tr>
          <tr>
            <td colspan="3">引渡し予定日</td>
            <td colspan="7">
              <input type="text" name="ankenDetailList[0].hkwEndYtd" id="hkwEndYtd" class="input-code-text hasDatepicker" maxlength="8" value=""><img class="ui-datepicker-trigger" src="./売却物件情報連携システム 案件詳細_files/cal.png" alt="カレンダーから選択" title="カレンダーから選択">
              <input type="hidden" id="orgHkwEndYtd" value="0">
            </td>
          </tr>
          <tr>
            <td colspan="3">売買後管理形態</td>
            <td colspan="7">
              <input type="radio" id="contactChoice1" name="ankenDetailList[0].shkeiStt" value="1">
              <label for="contactChoice1">借上継承</label>
              <input type="radio" id="contactChoice2" name="ankenDetailList[0].shkeiStt" value="2">
              <label for="contactChoice2">管理継承</label>
              <input type="radio" id="contactChoice3" name="ankenDetailList[0].shkeiStt" value="3">
              <label for="contactChoice3">合意解約</label>
            </td>
          </tr>
          <tr>
            <td colspan="10" style="color:red">売買後管理形態が「管理継承」である場合、パートナーズ営業所は速やかに「他社物ポータル」を起票してください</td>
          </tr>
          <tr>
            <td colspan="10">※管理のみ契約を継承する場合（管理のみ契約の名義変更）は、他社物ポータルの起票は不要です。</td>
          </tr>
          <tr>
            <td colspan="10" align="right">
              <input type="button" id="request_partners" onclick="requestPartners(&quot;3&quot;, &quot;要実施者：パートナーズ&quot;, &quot;パートナーズへ報告&quot;)" class="detail-button" value="パートナーズへ報告">
            </td>
          </tr>
          
        </tbody>
      </table>
    </div>
    <hr>
    <span><b>引渡完了</b><div align="right"><img src="./売却物件情報連携システム 案件詳細_files/icon_minus.png" id="showHide4" onclick="showHide(&quot;status4&quot;, &quot;showHide4&quot;)"></div></span>
    <div id="status4">
      
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
          </tr>
          <tr>
            <td colspan="10"><b>【報告】引渡完了（実施者：リーシング）</b></td>
          </tr>
          <tr>
            <td colspan="10">①引渡し日を入力し、画面最下部の「更新」を押下</td>
          </tr>
          <tr>
            <td colspan="10">②「パートナーズへ報告」を押下</td>
          </tr>
          <tr>
            <td colspan="3">引渡し日</td>
            <td colspan="6">
              <input type="text" name="ankenDetailList[0].hkwEnd" id="hkwEnd" class="input-code-text hasDatepicker" maxlength="8" value=""><img class="ui-datepicker-trigger" src="./売却物件情報連携システム 案件詳細_files/cal.png" alt="カレンダーから選択" title="カレンダーから選択">
            </td>
            <td align="right">
              <input type="button" onclick="requestPartners(&#39;5&#39;, &#39;要実施者：パートナーズ&#39;, &#39;パートナーズへ報告&#39;)" class="detail-button" value="パートナーズへ報告">
            </td>
          </tr>
          <tr>
            <td colspan="10">　</td>
          </tr>
        </tbody>
      </table>
    </div>
    <hr>
    <span><b>パートナーズ処理完了</b><div align="right"><img src="./売却物件情報連携システム 案件詳細_files/icon_minus.png" id="showHide5" onclick="showHide(&quot;status5&quot;, &quot;showHide5&quot;)"></div></span>
    <div id="status5">
      
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
          </tr>
          <tr>
            <td colspan="10"><b>【報告】引渡完了の報告受領（実施者：パートナーズ）</b></td>
          </tr>
          <tr>
            <td colspan="10">①リーシング売買による引渡しを終えたことを確認後、確認日を入力し、画面最下部の「更新」を押下</td>
          </tr>
          <tr>
            <td colspan="10">②「報告受領」を押下</td>
          </tr>
          <tr>
            <td colspan="3">確認日</td>
            <td colspan="6">
              <input type="text" name="ankenDetailList[0].shEnd" id="shEnd" class="input-code-text hasDatepicker" maxlength="8" value=""><img class="ui-datepicker-trigger" src="./売却物件情報連携システム 案件詳細_files/cal.png" alt="カレンダーから選択" title="カレンダーから選択">
            </td>
            <td align="right">
              <input type="button" onclick="requestLeasing(&#39;5&#39;, &#39;報告受領&#39;, &#39;報告受領&#39;)" class="detail-button" value="報告受領">
            </td>
          </tr>
          <tr>
            <td colspan="10">　</td>
          </tr>
        </tbody>
      </table>
    </div>
    <hr>
    <div>
      
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
          </tr>
          <tr>
            <td colspan="10">売却中止となった場合は案件キャンセルを実施</td>
          </tr>
          <tr>
            <td colspan="9">※案件キャンセルを実施するとステータスが「キャンセル」となります。</td>
            <td align="right">
              <input type="button" id="cancelBtn" class="detail-button" value="案件キャンセル">
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <hr>
    <div>
      
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
          </tr>
          <tr>
            <td colspan="10">コメント履歴</td>
          </tr>
          <tr>
            <td colspan="10">
              <textarea readonly="" spellcheck="false" rows="15" cols="100">
2024/07/25 10:07:00 (111638)
「文字化けテストmoji1234.xlsx」を添付しました。

              </textarea>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <footer>
    <div>
      
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
          </tr>
          <tr>
            <td colspan="9" rowspan="3" class="commentText">コメント登録欄(最大300文字：空白は１文字、改行は２文字を文字数に含む)
              <textarea name="comment" id="comment" rows="3" cols="100" maxlength="300"></textarea></td>
            <td>
              <input type="button" id="updateBtn" class="detail-button" value="更新">
              
            </td>
          </tr>
          <tr>
            <td>
              <button type="button" id="backBtn2" class="detail-button">案件一覧へ戻る</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div></footer>
    </form></div>
    <div>
<input type="hidden" name="_csrf" value="fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1">
</div>
  </div>
  

<div id="ui-datepicker-div" class="ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all"></div></body></html>