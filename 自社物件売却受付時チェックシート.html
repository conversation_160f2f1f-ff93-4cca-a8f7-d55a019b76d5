<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自社物件売却受付時チェックシート</title>
    <style>
        body {
            font-family: "MS Gothic", monospace;
            font-size: 12px;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 20px;
            border: 2px solid #000;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            padding: 10px;
            background-color: #e6e6e6;
            border: 1px solid #000;
            margin-bottom: 20px;
        }
        
        .section {
            margin-bottom: 20px;
            border: 1px solid #000;
        }
        
        .section-title {
            background-color: #d4edda;
            padding: 8px;
            font-weight: bold;
            border-bottom: 1px solid #000;
        }
        
        .section-content {
            padding: 10px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .checkbox-item {
            margin: 8px 0;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .numbered-list {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .numbered-list li {
            margin: 5px 0;
        }
        
        .input-field {
            border: none;
            border-bottom: 1px solid #000;
            background: transparent;
            padding: 2px;
            font-family: inherit;
            font-size: inherit;
        }
        
        .wide-input {
            width: 200px;
        }
        
        .medium-input {
            width: 100px;
        }
        
        .narrow-input {
            width: 60px;
        }
        
        .input-field:disabled {
            background-color: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }
        
        .footer-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #000;
        }
        
        .footer-left {
            display: flex;
            align-items: center;
            gap: 40px;
        }
        
        .footer-field {
            display: flex;
            align-items: center;
            gap: 10px;
            white-space: nowrap;
        }
        
        .footer-buttons {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #000;
            background-color: #e9ecef;
            cursor: pointer;
            font-family: inherit;
        }
        
        .btn:hover {
            background-color: #dee2e6;
        }
        
        .blue-section {
            background-color: #cce5ff;
        }
        
        .price-cell {
            text-align: right;
            width: 100px;
        }
        
        select.input-field {
            border: 1px solid #000;
            padding: 4px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            自社物件売却受付時チェックシート
        </div>
        
        <div class="section">
            <div class="section-title">【リーシング・パートナーズ　それぞれの対応について】</div>
            <div class="section-content">
                <p>※売却物件情報連携システム（以下、システム）対応についても記載しています。</p>
                
                <h4>≪リーシング対応フロー≫</h4>
                <p>① 自社物件受付時チェックシートの下記≪リーシング入力欄≫へ物件情報を入力のうえ、保存を行う</p>
                <p>② 保存後、システムの「パートナーズへ依頼」を押下してパートナーズへ対応依頼を行う</p>
                
                <h4>≪パートナーズ対応フロー≫</h4>
                <p>① リーシングからの対応依頼後、チェックシートを確認し、下記≪パートナーズ使用欄≫へ情報を入力のうえ、担当者・管理職の名前が表示される</p>
                <p>② チェックシートを記入後、下記添付資料の添付を行う</p>
                
                <p style="margin-left: 20px;">≪システム添付資料≫</p>
                <ol class="numbered-list" style="margin-left: 40px;">
                    <li>合意解約時賃貸人契約内容一覧表、２．追加営繕工事履歴一覧表、３．合意解約ご確認書、４．新特約解約金算出ツール、</li>
                    <li>一括賃貸借契約書または管理委託契約書、６．賃貸収入ご報告書、７．クレームWEBシステムの画面印刷</li>
                </ol>
                
                <p>③ 添付後、システムのガス会社（ガスパル o rそれ以外）を選択し、「リーシングへ回答」を押下して回答を行う</p>
                <p style="margin-left: 20px;">※上記③完了後は、オーナー様へ合意解約について訪問説明を実施し、システムへ署名捺印済みの合意解約ご確認書を添付のうえ、報告を行ってください。</p>
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">≪リーシング使用欄≫</div>
            <div class="section-content">
                <table>
                    <tr>
                        <td style="width: 12%;">建物名称</td>
                        <td style="width: 22%;"><input type="text" class="input-field" style="width: 100%;" disabled></td>
                        <td style="width: 12%;">契約形態</td>
                        <td style="width: 22%;"><input type="text" class="input-field" style="width: 100%;" disabled></td>
                        <td style="width: 12%;">現賃貸人名</td>
                        <td style="width: 22%;"><input type="text" class="input-field" style="width: 100%;" disabled></td>
                    </tr>
                    <tr>
                        <td style="width: 12%;">建物CD</td>
                        <td style="width: 22%;"><input type="text" class="input-field" style="width: 100%;" disabled></td>
                        <td style="width: 12%;">建物完成日</td>
                        <td style="width: 22%;"><input type="date" class="input-field" style="width: 100%;" disabled></td>
                        <td style="width: 12%;">建物種別</td>
                        <td style="width: 22%;"><input type="text" class="input-field" style="width: 100%;" disabled></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">≪パートナーズ使用欄≫</div>
            <div class="section-content">
                <h4>1. 承継される契約・特約等</h4>
                <table>
                    <tr>
                        <th>特約</th>
                        <th>該当有無</th>
                        <th>対象部屋（本居入部屋）</th>
                        <th class="price-cell">送約金・精算金（税込）</th>
                    </tr>
                    <tr>
                        <td>①回復特回復責任負担（礼特約）</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                    <tr>
                        <td>②新築特回復責任負担（礼特約）<br>（修繕制度等32%）</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                    <tr>
                        <td>③修繕諸費等含む3%は特約分入居なし</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                    <tr>
                        <td>④回復特回復責任負担（礼特約）<br>＝通常処理（修繕特約）</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                    <tr>
                        <td>⑤大手総合売電設計設定のための<br>居住営業（修繕契約書）</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                    <tr>
                        <td>⑥特別リフォーム共通</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                    <tr>
                        <td colspan="3" style="text-align: right;"><strong>①〜⑥の積算合計の合計</strong></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                </table>
                
                <h4>2. 建物不具合</h4>
                <p>※損DQNNA、工事担当の意見をぜひ参考にしてください。（夏以外時期的時の建物不具合の発生）</p>
                <table>
                    <tr>
                        <th>項目</th>
                        <th>該当有無</th>
                        <th>詳細状況</th>
                    </tr>
                    <tr>
                        <td>①雨漏り</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                    </tr>
                    <tr>
                        <td>②シロアリ</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                    </tr>
                    <tr>
                        <td>③地盤沈下</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                    </tr>
                    <tr>
                        <td>④その他</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                    </tr>
                </table>
                
                <h4>3. ガス会社</h4>
                <p>※担当者については分からない場合、記載</p>
                <table>
                    <tr>
                        <th>ガス会社名</th>
                        <th>連絡先</th>
                        <th>担当者</th>
                    </tr>
                    <tr>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td><input type="text" class="input-field medium-input"></td>
                    </tr>
                </table>
                
                <h4>4. その他の項目</h4>
                <table>
                    <tr>
                        <th>加入内容/他</th>
                        <th>該当有無</th>
                        <th>加入プラン・金額等</th>
                    </tr>
                    <tr>
                        <td>①ファンティサービス</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                    </tr>
                    <tr>
                        <td>②オーナーズガード</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                    </tr>
                    <tr>
                        <td>③ガスバルローン（支払業務連絡確認必須）</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                    </tr>
                    <tr>
                        <td>④DK SELECT ネットサービス<br>（設備用具、ランニングコスト）</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                    </tr>
                    <tr>
                        <td>⑤著名各）■干涸ライズ社 ■NURのニー社<br>■・COM ■スターキット</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                    </tr>
                </table>
                
                <h4>5. 継承が不可能</h4>
                <table>
                    <tr>
                        <th>項目名</th>
                        <th>該当有無</th>
                        <th>備考</th>
                        <th class="price-cell">送約金・精算金（税込）</th>
                    </tr>
                    <tr>
                        <td>①リフォームローン</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                    <tr>
                        <td>②オーロラ</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                    <tr>
                        <td>③修り上げ手数料証金</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                    <tr>
                        <td>④その他</td>
                        <td><input type="checkbox"> 有</td>
                        <td><input type="text" class="input-field wide-input"></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                    <tr>
                        <td colspan="3" style="text-align: right;"><strong>①〜④の積算合計の合計</strong></td>
                        <td class="price-cell"><input type="text" class="input-field narrow-input"> 円</td>
                    </tr>
                </table>
                
                <h4>6. 入居賃料連動型（原・事半）</h4>
                <p>※償上期間は第30年法（第30年税・償上期間が3年を未満の場合、償上承継3年間で終了）</p>
                <table>
                    <tr>
                        <th>対象物件の借上形態</th>
                        <th>残存償上期間（●年●ヶ月）</th>
                        <th>備考</th>
                    </tr>
                    <tr>
                        <td>
                            <select class="input-field" style="width: 100%; border: 1px solid #000; padding: 4px;">
                                <option value="">選択してください</option>
                                <option value="30年一括">30年一括</option>
                                <option value="賃料連動型">賃料連動型</option>
                                <option value="保証30年型">保証30年型</option>
                                <option value="35年一括">35年一括</option>
                                <option value="管理のみ">管理のみ</option>
                            </select>
                        </td>
                        <td><input type="text" class="input-field narrow-input"> 年 <input type="text" class="input-field narrow-input"> ヶ月</td>
                        <td><input type="text" class="input-field wide-input"></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="footer-section">
            <div class="footer-left">
                <div class="footer-field">
                    <span>担当者（パートナーズ）</span>
                    <input type="text" class="input-field wide-input">
                </div>
                <div class="footer-field">
                    <span>管理者（パートナーズ）</span>
                    <input type="text" class="input-field wide-input">
                </div>
            </div>
            <div class="footer-buttons">
                <button class="btn" onclick="saveForm()">保存</button>
                <button class="btn" onclick="clearForm()">クリア</button>
                <button class="btn" onclick="goToIndex()">条件詳細へ戻る</button>
            </div>
        </div>
    </div>
    
    <script>
        function saveForm() {
            // Save form data to memory
            const formData = new FormData();
            const inputs = document.querySelectorAll('input, select');
            inputs.forEach(input => {
                if (input.type === 'checkbox') {
                    formData.append(input.id, input.checked);
                } else {
                    formData.append(input.name || input.id, input.value);
                }
            });
            
            // Store in memory (since localStorage is not available)
            window.savedFormData = formData;
            alert('フォームが保存されました');
        }
        
        function clearForm() {
            if (confirm('フォームをクリアしますか？')) {
                const inputs = document.querySelectorAll('input, select');
                inputs.forEach(input => {
                    if (input.type === 'checkbox') {
                        input.checked = false;
                    } else {
                        input.value = '';
                    }
                });
                alert('フォームがクリアされました');
            }
        }
        
        function goToIndex() {
            alert('条件詳細画面に戻ります');
            // This would typically navigate to another page
        }
        
        // Auto-calculate totals when values change
        document.addEventListener('input', function(e) {
            if (e.target.type === 'text' && e.target.closest('.price-cell')) {
                calculateTotals();
            }
        });
        
        function calculateTotals() {
            // Calculate section 1 total
            const section1Inputs = document.querySelectorAll('table:nth-of-type(2) .price-cell input');
            let section1Total = 0;
            section1Inputs.forEach(input => {
                if (input.value && !isNaN(input.value)) {
                    section1Total += parseInt(input.value);
                }
            });
            
            // Calculate section 5 total
            const section5Inputs = document.querySelectorAll('table:nth-of-type(7) .price-cell input');
            let section5Total = 0;
            section5Inputs.forEach(input => {
                if (input.value && !isNaN(input.value)) {
                    section5Total += parseInt(input.value);
                }
            });
        }
    </script>
</body>
</html>