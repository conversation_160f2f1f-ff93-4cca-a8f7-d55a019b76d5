<!DOCTYPE html PUBLIC "-//W3C//Dtd HTML 4.01 transitional//EN" "http://www.w3.org/tr/html4/loose.dtd">
<!-- saved from url=(0802)http://devap9001/BaibaiJohoKanri/rentroll?ankenNo=000000368&bldCd=011278901&bldCd=011278902&bldCd=011278903&bldCd=011278904&bldNm=%E3%82%A8%E3%83%A0%E3%83%BB%E2%97%8B%E3%83%AB%E2%97%8B%E3%82%A8%E2%97%8B&bldNm=%E3%82%A8%E3%83%A0%E3%83%BB%E2%97%8B%E3%83%AB%E2%97%8B%E3%82%A8%E2%97%8B&bldNm=%E3%82%A8%E3%83%A0%E3%83%BB%E2%97%8B%E3%83%AB%E2%97%8B%E3%82%A8%E2%97%8B&bldNm=%E3%82%A8%E3%83%A0%E3%83%BB%E2%97%8B%E3%83%AB%E2%97%8B%E3%82%A8%E2%97%8B&_csrf=fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1&stoNm=%E7%99%BD%E7%9F%B3%E9%A7%85%E5%89%8D%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80&stotmNm=%E5%90%89%E3%81%82%E3%80%80%E3%81%82%E3%81%82%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80%E3%80%80&fomRvd=20240724 -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  
  <title>売却物件情報連携システム レントロール</title>

  
  <link href="./売却物件情報連携システム レントロール_files/common.css" rel="stylesheet">

  
  <script src="./売却物件情報連携システム レントロール_files/jquery-2.2.0.min.js.ダウンロード"></script>
  
  <script src="./売却物件情報連携システム レントロール_files/control.js.ダウンロード"></script>

<script type="text/javascript">
<!--
$(function() {
    $("input,select").change(function() {
        $(this).css({
            backgroundColor: 'pink'
        });
    })

    // データ取込ボタン押下時の確認メッセージ
    $('#readBtn').click(function(e) {
        if(!confirm('データ取込を行います。よろしいですか？')){
            /* キャンセルの時の処理 */
            return false;
        }
        $('form').attr('action', '/BaibaiJohoKanri/rentroll/read');
        $('form').submit();
        return true;
    });

    // 保存ボタン押下時の確認メッセージ
    $('#modBtn').click(function(e) {
        if (!checkInputData()) {
            return false;
        }
        if(!confirm('保存してよろしいですか？')){
            /* キャンセルの時の処理 */
            return false;
        }
        $('form').attr('action', '/BaibaiJohoKanri/rentroll/mod');
        $('form').submit();
        return true;
    });

    // pdf出力ボタン押下時の確認メッセージ
    $('#pdfBtn').click(function(e) {
        if($('#fomRvd').val() == 0) {
            alert('レントロールの確認日が入力されていません。');
            return false;
        }
        if(!confirm('PDF出力を行いますが、よろしいですか？')){
            /* キャンセルの時の処理 */
            return false;
        }
        $('form').attr('action', '/BaibaiJohoKanri/rentroll/pdfOut');
        $('form').submit();
        return true;
    });

    // 案件詳細へ戻るボタンクリック時の処理
    $('#backToDetailBtn').click(function() {
        window.location.href = '売却物件情報連携システム 案件詳細.html';
    });

});

// 文字列(半角)8桁のチェック
function checkCharacter8(objId, name) {
    if($(objId).val().length > 8) {
        alert(name + 'は８文字以下で入力してください');
        $(objId).focus();
        return false;
    }
    if (!$(objId).val().match(/^[A-Za-z0-9]*$/)) {
        alert(name + 'は半角英数字で入力してください');
        $(objId).focus();
        return false;
    }
    return true;
}

// 文字列(全角のみ)10桁のチェック
function checkCharacter10(objId, name) {
    if($(objId).val() == "") {
        return true;
    }
    if($(objId).val().length > 10) {
        alert(name + 'は１０文字以下で入力してください');
        $(objId).focus();
        return false;
    }
    // 全角文字コードチェック
    if ($(objId).val().match(/^[^\x01-\x7E\uFF61-\uFF9F]+$/)) {
        // 全角文字
        return true;
    } else {
        // 全角文字以外
        alert(name + 'は全角文字で入力してください');
        $(objId).focus();
        return false;
    }
    return true;
}

// 文字列(全角のみ)20桁のチェック
function checkCharacter20(objId, name) {
    if($(objId).val() == "") {
        return true;
    }
    if($(objId).val().length > 20) {
        alert(name + 'は２０文字以下で入力してください');
        $(objId).focus();
        return false;
    }
    // 全角文字コードチェック
    if ($(objId).val().match(/^[^\x01-\x7E\uFF61-\uFF9F]+$/)) {
        // 全角文字
        return true;
    } else {
        // 全角文字以外
        alert(name + 'は全角文字で入力してください');
        $(objId).focus();
        return false;
    }
    return true;
}

// 文字列(半角、ハイフンあり)15桁のチェック
function checkCharacter15(objId, name) {
    if($(objId).val() == "") {
        return true;
    }
    if($(objId).val().length > 15) {
        alert(name + 'は１５桁以下で入力してください');
        $(objId).focus();
        return false;
    }
    if (!$(objId).val().match(/^[-0-9]*$/)) {
        alert(name + 'は半角数値およびハイフンで入力してください');
        $(objId).focus();
        return false;
    }
    return true;
}

// 面積(㎡)のチェック
// 整数5桁、小数2桁
function checkAraM2(objId, name) {
    var araM2 = $(objId).val().replaceAll(",","");
    if(araM2 == "") {
        alert(name + 'が入力されていません');
        $(objId).focus();
        return false;
    }
    // 全体の桁数チェック(小数込みで8桁まで許容)
    if(araM2.length > 8) {
        alert(name + 'は整数５桁、小数２桁以下で入力してください');
        $(objId).focus();
        return false;
    }
    // 整数のみの場合
    if(araM2.split('.').length == 1) {
        if(araM2.length > 5) {
            alert(name + 'は整数５桁以下で入力してください');
            $(objId).focus();
            return false;
        }
    } else if(araM2.split('.').length == 2) {
        if(araM2.split('.')[0].length > 5 || araM2.split('.')[0].length <= 0) {
            alert(name + 'は整数５桁以下で入力してください');
            $(objId).focus();
            return false;
        }
        if(araM2.split('.')[1].length > 2 || araM2.split('.')[1].length <= 0) {
            alert(name + 'は小数２桁以下で入力してください');
            $(objId).focus();
            return false;
        }
    } else {
        // 入力値に小数点(.)が2つ以上含まれる場合
        alert(name + 'は整数５桁、小数２桁以下で入力してください');
        $(objId).focus();
        return false;
    }

    if (!araM2.match(/^[0-9.]*$/)) {
        alert(name + 'は半角数値で入力してください');
        $(objId).focus();
        return false;
    }
    return true;
}

// code11桁の入力チェック
function checkCode11(objId, name) {
    var value = $(objId).val().replaceAll(",","");
    if(value == "") {
        alert(name + 'が入力されていません');
        $(objId).focus();
        return false;
    }
    if(value.length > 11) {
        alert(name + 'は１１桁以下で入力してください');
        $(objId).focus();
        return false;
    }
    if (!value.match(/^[0-9]*$/)) {
        alert(name + 'は半角数値で入力してください');
        $(objId).focus();
        return false;
    }
    return true;
}
  
// 日付の入力チェック
function checkInputDate(objId, name) {
    if($(objId).val() == "" || $(objId).val() == "0") {
        // ブランク、0は許容
        return true;
    }
    var date = $(objId).val().replaceAll("/","");
    if(date.length != 8 || !date.match(/^[0-9]*$/)) {
        alert(name + 'は８桁の半角数値で入力してください\n例：2024年3月10日→20240310');
        $(objId).focus();
        return false;
    }
    return true;
}

// 入力チェック
function checkInputData() {
    // 明細部
    var ankenRentRollListSize = $("#ankenRentRollListSize").val();
    for (var i = 0; i < ankenRentRollListSize; i++) {
        var rentRollListSize = $("#" + "rentRollList_" + (i+1) + "_size").val();
        for (var j = 0; j < rentRollListSize; j++) {
            // 間取
            if (!checkCharacter8("#madr_" + (i+1) + "_" + (j+1), "間取")) {
                return false;
            }
            // 面積(㎡)
            if (!checkAraM2("#araM2_" + (i+1) + "_" + (j+1), "面積(㎡)")) {
                return false;
            }
            // 月額の賃料
            if (!checkCode11("#mgNoRnr_" + (i+1) + "_" + (j+1), "月額賃料")) {
                return false;
            }
            // 月額の共益費
            if (!checkCode11("#mgNokeh_" + (i+1) + "_" + (j+1), "月額共益費")) {
                return false;
            }
            // 月額の駐車場
            if (!checkCode11("#mgNoPk_" + (i+1) + "_" + (j+1), "月額駐車料")) {
                return false;
            }
            // 敷金
            if (!checkCode11("#skk_" + (i+1) + "_" + (j+1), "敷金")) {
                return false;
            }
            // 契約開始日
            if (!checkInputDate("#kyStd_" + (i+1) + "_" + (j+1), "契約開始日")) {
                return false;
            }
            // 入居中の手取り家賃
            if (!checkCode11("#nkcNoTdrYc_" + (i+1) + "_" + (j+1), "入居中の手取り家賃")) {
                return false;
            }
            // 空室の手取り保証
            if (!checkCode11("#armNoTdrYc_" + (i+1) + "_" + (j+1), "空室（募集条件）の手取り保証")) {
                return false;
            }
            // 空室（募集条件）の賃料
            if (!checkCode11("#armBoshJknNoRnr_" + (i+1) + "_" + (j+1), "空室（募集条件）の賃料")) {
                return false;
            }
            // 空室（募集条件）の共益費
            if (!checkCode11("#armBoshJknNoKeh_" + (i+1) + "_" + (j+1), "空室（募集条件）の共益費")) {
                return false;
            }
            // 空室（募集条件）の駐車場
            if (!checkCode11("#armBoshJknNoPk_" + (i+1) + "_" + (j+1), "空室（募集条件）の駐車料")) {
                return false;
            }
        }
    }
    
    // リーシング使用欄
    // 販売価格
    if (!checkCode11("#slpr", "販売価格")) {
        return false;
    }
    // 担当店
    if (!checkCharacter10("#ttTn", "担当店")) {
        return false;
    }
    // 担当者
    if (!checkCharacter10("#ttm", "担当者")) {
        return false;
    }
    // 連絡先
    if (!checkCharacter15("#rrs", "連絡先")) {
        return false;
    }
    
    // 支出項目
    var ankenExpenditureItemListSize = $("#ankenExpenditureItemListSize").val();
    for (var i = 0; i < ankenExpenditureItemListSize; i++) {
        var expenditureListSize = $("#" + "expenditureItemList_" + (i+1) + "_size").val();
        for (var j = 0; j < expenditureListSize; j++) {
            // 支出項目名
            if (!checkCharacter20("#ssht_kmm_" + (i+1) + "_" + (j+1), "支出項目(月額)")) {
                return false;
            }
            // 支出金額
            if (!checkCode11("#ssht_kg_" + (i+1) + "_" + (j+1), "支出金額")) {
                return false;
            }
        }
    }
    
    var ankenIncomeItemListSize = $("#ankenIncomeItemListSize").val();
    for (var i = 0; i < ankenIncomeItemListSize; i++) {
        var incomeListSize = $("#" + "incomeItemList_" + (i+1) + "_size").val();
        for (var j = 0; j < incomeListSize; j++) {
            // 収入項目名
            if (!checkCharacter20("#icm_kmm_" + (i+1) + "_" + (j+1), "収入項目(月額)")) {
                return false;
            }
            // 収入金額
            if (!checkCode11("#icm_kg_" + (i+1) + "_" + (j+1), "収入金額")) {
                return false;
            }
        }
    }
    return true;
}

$(window).load(function(){
    $("form select[readonly] > option:not(:selected)").attr('disabled', 'disabled');
})

//-->
</script>
</head>
<body>
  <div style="text-align: center;">
    
    <h2 style="display: inline-block;">売却物件情報連携システム</h2>
  </div>

  <div class="rentroll-container">
    <div class="rentroll-contents rentroll-font">
    <form id="form" action="http://devap9001/BaibaiJohoKanri/detail?ankenNo=000000368&amp;_csrf=fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1" method="POST">
    <div class="inputClass">
      <span>
        <button type="button" id="backToDetailBtn" class="rentroll-button">案件詳細へ戻る</button>
        <label><input type="checkbox" name="knrKshoNas" value="1">管理継承なし</label>
        <button type="button" class="rentroll-button" id="readBtn">データ取込</button>
        <button type="button" class="rentroll-button" id="modBtn">保存</button>
        <button type="button" class="rentroll-button" id="pdfBtn">PDF出力</button>
      </span>
    </div>
    <br>
    <input type="hidden" name="ankenNo" value="000000368">
    
      <input type="hidden" name="bldCd" value="011278901">
    
      <input type="hidden" name="bldCd" value="011278902">
    
      <input type="hidden" name="bldCd" value="011278903">
    
      <input type="hidden" name="bldCd" value="011278904">
    
      <input type="hidden" name="bldNm" value="エム・○ル○エ○">
    
      <input type="hidden" name="bldNm" value="エム・○ル○エ○">
    
      <input type="hidden" name="bldNm" value="エム・○ル○エ○">
    
      <input type="hidden" name="bldNm" value="エム・○ル○エ○">
    
    <input type="hidden" name="adr" value="岐阜県　瑞穂市　本田　字高田○６○番○１">
    <input type="hidden" name="stoNm" value="白石駅前　　　　　　　　　　　　　　　　">
    <input type="hidden" name="stotmNm" value="吉あ　ああ　　　　　　　">
    <input type="hidden" name="fomRvd" id="fomRvd" value="20240724">
    <input type="hidden" name="ankenRentRollListSize" id="ankenRentRollListSize" value="4">
    
      <input type="hidden" name="rentRollList_1_size" id="rentRollList_1_size" value="5">
    
      <input type="hidden" name="rentRollList_2_size" id="rentRollList_2_size" value="10">
    
      <input type="hidden" name="rentRollList_3_size" id="rentRollList_3_size" value="10">
    
      <input type="hidden" name="rentRollList_4_size" id="rentRollList_4_size" value="11">
    
    <input type="hidden" name="ankenIncomeItemListSize" id="ankenIncomeItemListSize" value="4">
    
      <input type="hidden" name="incomeItemList_1_size" id="incomeItemList_1_size" value="32">
    
      <input type="hidden" name="incomeItemList_2_size" id="incomeItemList_2_size" value="8">
    
      <input type="hidden" name="incomeItemList_3_size" id="incomeItemList_3_size" value="4">
    
      <input type="hidden" name="incomeItemList_4_size" id="incomeItemList_4_size" value="5">
    
    <input type="hidden" name="ankenExpenditureItemListSize" id="ankenExpenditureItemListSize" value="4">
    
      <input type="hidden" name="expenditureItemList_1_size" id="expenditureItemList_1_size" value="32">
    
      <input type="hidden" name="expenditureItemList_2_size" id="expenditureItemList_2_size" value="8">
    
      <input type="hidden" name="expenditureItemList_3_size" id="expenditureItemList_3_size" value="4">
    
      <input type="hidden" name="expenditureItemList_4_size" id="expenditureItemList_4_size" value="5">
    
    <table border="0" align="center">
      <tbody>
        <tr>
          <td colspan="3" style="padding-left: 4px">
            <table border="1" align="center" style="margen:0px">
              <thead class="rentroll-table-thead">
                
                    <tr>
                      <td colspan="3">案件No</td>
                      <td colspan="15">000000368</td>
                      <td colspan="2">2024/07/24</td>
                      <td colspan="3">時点作成</td>
                    </tr>
                  
                <tr>
                  <td rowspan="2" class="thead-rentroll-24">No</td>
                  <td rowspan="2" class="thead-rentroll-44">種別</td>
                  <td rowspan="2" class="thead-rentroll-80">状況</td>
                  <td rowspan="2" class="thead-rentroll-60">契約者</td>
                  <td rowspan="2" class="thead-rentroll-70">用途</td>
                  <td rowspan="2" class="thead-rentroll-44">間取</td>
                  <td colspan="2" class="thead-rentroll-80">面積</td>
                  <td colspan="4" class="thead-rentroll-244">月額</td>
                  <td rowspan="2" class="thead-rentroll-64">坪賃料</td>
                  <td rowspan="2" class="thead-rentroll-64">敷金</td>
                  <td rowspan="2" class="thead-rentroll-70">保証会社</td>
                  <td rowspan="2" class="thead-rentroll-74">契約開始日</td>
                  <td class="thead-rentroll-64">入居中</td>
                  <td class="thead-rentroll-64">空室</td>
                  <td colspan="4" class="thead-rentroll-244">空室（募集条件）</td>
                </tr>
                <tr>
                  <td class="thead-rentroll-64">㎡</td>
                  <td class="thead-rentroll-64">坪</td>
                  <td class="thead-rentroll-64">賃料</td>
                  <td class="thead-rentroll-64">共益費</td>
                  <td class="thead-rentroll-64">駐車場</td>
                  <td class="thead-rentroll-64">合計</td>
                  <td>手取り家賃</td>
                  <td>手取り保証</td>
                  <td class="thead-rentroll-64">賃料</td>
                  <td class="thead-rentroll-64">共益費</td>
                  <td class="thead-rentroll-64">駐車場</td>
                  <td class="thead-rentroll-64">合計</td>
                </tr>
                </thead>
        <tbody class="rentroll-table-tbody">
         
                <input type="hidden" name="rentRollList[0][0].bldCd" value="011278901">
                
                <tr>
                  <td class="output-rentroll-first-row" colspan="24">エム・○ル○エ○ (建物CD：011278901)</td>
                </tr>
                
                <tr>
                  <td><input type="text" name="rentRollList[0][0].msiNo" id="msiNo_1_1" value="1" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[0][0].sb" id="sb_1_1" value="101" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[0][0].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中" selected="">入居中</option>
                            <option value="募集中">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[0][0].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[0][0].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[0][0].madr" maxlength="8" id="madr_1_1" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].araM2" maxlength="8" id="araM2_1_1" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].araTb" id="araTb_1_1" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].mgNoRnr" maxlength="11" id="mgNoRnr_1_1" value="42,000" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].mgNoKeh" maxlength="11" id="mgNokeh_1_1" value="4,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].mgNoPk" maxlength="11" id="mgNoPk_1_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].mgNoGk" id="mgNoGk_1_1" value="46,500" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].tbRnr" id="tbRnr_1_1" value="2,642" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].skk" maxlength="11" id="skk_1_1" value="0" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[0][0].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有" selected="">有</option>
                            <option value="無">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[0][0].kyStd" maxlength="10" id="kyStd_1_1" value="2018/03/18" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_1_1" value="38,010" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].armNoTdrYc" maxlength="11" id="armNoTdrYc_1_1" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_1_1" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_1_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_1_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][0].armBoshJknNoGk" id="armBoshJknNoGk_1_1" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[0][1].bldCd" value="011278901">
                
                <tr>
                  <td><input type="text" name="rentRollList[0][1].msiNo" id="msiNo_1_2" value="2" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[0][1].sb" id="sb_1_2" value="102" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[0][1].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中" selected="">入居中</option>
                            <option value="募集中">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[0][1].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[0][1].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[0][1].madr" maxlength="8" id="madr_1_2" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].araM2" maxlength="8" id="araM2_1_2" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].araTb" id="araTb_1_2" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].mgNoRnr" maxlength="11" id="mgNoRnr_1_2" value="42,000" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].mgNoKeh" maxlength="11" id="mgNokeh_1_2" value="4,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].mgNoPk" maxlength="11" id="mgNoPk_1_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].mgNoGk" id="mgNoGk_1_2" value="46,500" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].tbRnr" id="tbRnr_1_2" value="2,642" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].skk" maxlength="11" id="skk_1_2" value="0" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[0][1].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有" selected="">有</option>
                            <option value="無">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[0][1].kyStd" maxlength="10" id="kyStd_1_2" value="2018/04/01" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_1_2" value="38,010" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].armNoTdrYc" maxlength="11" id="armNoTdrYc_1_2" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_1_2" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_1_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_1_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][1].armBoshJknNoGk" id="armBoshJknNoGk_1_2" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[0][2].bldCd" value="011278901">
                
                <tr>
                  <td><input type="text" name="rentRollList[0][2].msiNo" id="msiNo_1_3" value="3" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[0][2].sb" id="sb_1_3" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[0][2].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[0][2].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[0][2].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[0][2].madr" maxlength="8" id="madr_1_3" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].araM2" maxlength="8" id="araM2_1_3" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].araTb" id="araTb_1_3" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].mgNoRnr" maxlength="11" id="mgNoRnr_1_3" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].mgNoKeh" maxlength="11" id="mgNokeh_1_3" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].mgNoPk" maxlength="11" id="mgNoPk_1_3" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].mgNoGk" id="mgNoGk_1_3" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].tbRnr" id="tbRnr_1_3" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].skk" maxlength="11" id="skk_1_3" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[0][2].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[0][2].kyStd" maxlength="10" id="kyStd_1_3" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_1_3" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].armNoTdrYc" maxlength="11" id="armNoTdrYc_1_3" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_1_3" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_1_3" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_1_3" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][2].armBoshJknNoGk" id="armBoshJknNoGk_1_3" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[0][3].bldCd" value="011278901">
                
                <tr>
                  <td><input type="text" name="rentRollList[0][3].msiNo" id="msiNo_1_4" value="4" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[0][3].sb" id="sb_1_4" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[0][3].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[0][3].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[0][3].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[0][3].madr" maxlength="8" id="madr_1_4" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].araM2" maxlength="8" id="araM2_1_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].araTb" id="araTb_1_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].mgNoRnr" maxlength="11" id="mgNoRnr_1_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].mgNoKeh" maxlength="11" id="mgNokeh_1_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].mgNoPk" maxlength="11" id="mgNoPk_1_4" value="5,250" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].mgNoGk" id="mgNoGk_1_4" value="5,250" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].tbRnr" id="tbRnr_1_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].skk" maxlength="11" id="skk_1_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[0][3].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[0][3].kyStd" maxlength="10" id="kyStd_1_4" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_1_4" value="4,975" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].armNoTdrYc" maxlength="11" id="armNoTdrYc_1_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_1_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_1_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_1_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][3].armBoshJknNoGk" id="armBoshJknNoGk_1_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[0][4].bldCd" value="011278901">
                
                <tr>
                  <td><input type="text" name="rentRollList[0][4].msiNo" id="msiNo_1_5" value="5" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[0][4].sb" id="sb_1_5" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[0][4].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[0][4].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[0][4].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[0][4].madr" maxlength="8" id="madr_1_5" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].araM2" maxlength="8" id="araM2_1_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].araTb" id="araTb_1_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].mgNoRnr" maxlength="11" id="mgNoRnr_1_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].mgNoKeh" maxlength="11" id="mgNokeh_1_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].mgNoPk" maxlength="11" id="mgNoPk_1_5" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].mgNoGk" id="mgNoGk_1_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].tbRnr" id="tbRnr_1_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].skk" maxlength="11" id="skk_1_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[0][4].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[0][4].kyStd" maxlength="10" id="kyStd_1_5" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_1_5" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].armNoTdrYc" maxlength="11" id="armNoTdrYc_1_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_1_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_1_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_1_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[0][4].armBoshJknNoGk" id="armBoshJknNoGk_1_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[1][0].bldCd" value="011278902">
                
                <tr>
                  <td class="output-rentroll-first-row" colspan="24">エム・○ル○エ○ (建物CD：011278902)</td>
                </tr>
                
                <tr>
                  <td><input type="text" name="rentRollList[1][0].msiNo" id="msiNo_2_1" value="1" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[1][0].sb" id="sb_2_1" value="101" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[1][0].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中" selected="">入居中</option>
                            <option value="募集中">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][0].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][0].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][0].madr" maxlength="8" id="madr_2_1" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].araM2" maxlength="8" id="araM2_2_1" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].araTb" id="araTb_2_1" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].mgNoRnr" maxlength="11" id="mgNoRnr_2_1" value="59,000" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].mgNoKeh" maxlength="11" id="mgNokeh_2_1" value="4,700" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].mgNoPk" maxlength="11" id="mgNoPk_2_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].mgNoGk" id="mgNoGk_2_1" value="63,700" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].tbRnr" id="tbRnr_2_1" value="3,619" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].skk" maxlength="11" id="skk_2_1" value="118,000" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[1][0].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有">有</option>
                            <option value="無" selected="">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][0].kyStd" maxlength="10" id="kyStd_2_1" value="2009/10/01" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_2_1" value="38,010" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].armNoTdrYc" maxlength="11" id="armNoTdrYc_2_1" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_2_1" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_2_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_2_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][0].armBoshJknNoGk" id="armBoshJknNoGk_2_1" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[1][1].bldCd" value="011278902">
                
                <tr>
                  <td><input type="text" name="rentRollList[1][1].msiNo" id="msiNo_2_2" value="2" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[1][1].sb" id="sb_2_2" value="102" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[1][1].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中" selected="">入居中</option>
                            <option value="募集中">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][1].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][1].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][1].madr" maxlength="8" id="madr_2_2" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].araM2" maxlength="8" id="araM2_2_2" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].araTb" id="araTb_2_2" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].mgNoRnr" maxlength="11" id="mgNoRnr_2_2" value="41,000" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].mgNoKeh" maxlength="11" id="mgNokeh_2_2" value="4,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].mgNoPk" maxlength="11" id="mgNoPk_2_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].mgNoGk" id="mgNoGk_2_2" value="45,500" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].tbRnr" id="tbRnr_2_2" value="2,585" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].skk" maxlength="11" id="skk_2_2" value="0" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[1][1].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有" selected="">有</option>
                            <option value="無">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][1].kyStd" maxlength="10" id="kyStd_2_2" value="2019/05/01" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_2_2" value="37,105" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].armNoTdrYc" maxlength="11" id="armNoTdrYc_2_2" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_2_2" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_2_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_2_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][1].armBoshJknNoGk" id="armBoshJknNoGk_2_2" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[1][2].bldCd" value="011278902">
                
                <tr>
                  <td><input type="text" name="rentRollList[1][2].msiNo" id="msiNo_2_3" value="3" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[1][2].sb" id="sb_2_3" value="103" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[1][2].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中" selected="">入居中</option>
                            <option value="募集中">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][2].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][2].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][2].madr" maxlength="8" id="madr_2_3" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].araM2" maxlength="8" id="araM2_2_3" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].araTb" id="araTb_2_3" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].mgNoRnr" maxlength="11" id="mgNoRnr_2_3" value="46,000" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].mgNoKeh" maxlength="11" id="mgNokeh_2_3" value="4,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].mgNoPk" maxlength="11" id="mgNoPk_2_3" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].mgNoGk" id="mgNoGk_2_3" value="50,500" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].tbRnr" id="tbRnr_2_3" value="2,869" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].skk" maxlength="11" id="skk_2_3" value="0" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[1][2].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有" selected="">有</option>
                            <option value="無">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][2].kyStd" maxlength="10" id="kyStd_2_3" value="2017/09/01" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_2_3" value="41,630" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].armNoTdrYc" maxlength="11" id="armNoTdrYc_2_3" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_2_3" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_2_3" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_2_3" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][2].armBoshJknNoGk" id="armBoshJknNoGk_2_3" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[1][3].bldCd" value="011278902">
                
                <tr>
                  <td><input type="text" name="rentRollList[1][3].msiNo" id="msiNo_2_4" value="4" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[1][3].sb" id="sb_2_4" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[1][3].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][3].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][3].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][3].madr" maxlength="8" id="madr_2_4" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].araM2" maxlength="8" id="araM2_2_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].araTb" id="araTb_2_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].mgNoRnr" maxlength="11" id="mgNoRnr_2_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].mgNoKeh" maxlength="11" id="mgNokeh_2_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].mgNoPk" maxlength="11" id="mgNoPk_2_4" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].mgNoGk" id="mgNoGk_2_4" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].tbRnr" id="tbRnr_2_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].skk" maxlength="11" id="skk_2_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[1][3].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][3].kyStd" maxlength="10" id="kyStd_2_4" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_2_4" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].armNoTdrYc" maxlength="11" id="armNoTdrYc_2_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_2_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_2_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_2_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][3].armBoshJknNoGk" id="armBoshJknNoGk_2_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[1][4].bldCd" value="011278902">
                
                <tr>
                  <td><input type="text" name="rentRollList[1][4].msiNo" id="msiNo_2_5" value="5" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[1][4].sb" id="sb_2_5" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[1][4].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][4].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][4].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][4].madr" maxlength="8" id="madr_2_5" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].araM2" maxlength="8" id="araM2_2_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].araTb" id="araTb_2_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].mgNoRnr" maxlength="11" id="mgNoRnr_2_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].mgNoKeh" maxlength="11" id="mgNokeh_2_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].mgNoPk" maxlength="11" id="mgNoPk_2_5" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].mgNoGk" id="mgNoGk_2_5" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].tbRnr" id="tbRnr_2_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].skk" maxlength="11" id="skk_2_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[1][4].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][4].kyStd" maxlength="10" id="kyStd_2_5" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_2_5" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].armNoTdrYc" maxlength="11" id="armNoTdrYc_2_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_2_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_2_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_2_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][4].armBoshJknNoGk" id="armBoshJknNoGk_2_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[1][5].bldCd" value="011278902">
                
                <tr>
                  <td><input type="text" name="rentRollList[1][5].msiNo" id="msiNo_2_6" value="6" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[1][5].sb" id="sb_2_6" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[1][5].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][5].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][5].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][5].madr" maxlength="8" id="madr_2_6" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].araM2" maxlength="8" id="araM2_2_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].araTb" id="araTb_2_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].mgNoRnr" maxlength="11" id="mgNoRnr_2_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].mgNoKeh" maxlength="11" id="mgNokeh_2_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].mgNoPk" maxlength="11" id="mgNoPk_2_6" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].mgNoGk" id="mgNoGk_2_6" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].tbRnr" id="tbRnr_2_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].skk" maxlength="11" id="skk_2_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[1][5].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][5].kyStd" maxlength="10" id="kyStd_2_6" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_2_6" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].armNoTdrYc" maxlength="11" id="armNoTdrYc_2_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_2_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_2_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_2_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][5].armBoshJknNoGk" id="armBoshJknNoGk_2_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[1][6].bldCd" value="011278902">
                
                <tr>
                  <td><input type="text" name="rentRollList[1][6].msiNo" id="msiNo_2_7" value="7" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[1][6].sb" id="sb_2_7" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[1][6].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][6].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][6].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][6].madr" maxlength="8" id="madr_2_7" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].araM2" maxlength="8" id="araM2_2_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].araTb" id="araTb_2_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].mgNoRnr" maxlength="11" id="mgNoRnr_2_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].mgNoKeh" maxlength="11" id="mgNokeh_2_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].mgNoPk" maxlength="11" id="mgNoPk_2_7" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].mgNoGk" id="mgNoGk_2_7" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].tbRnr" id="tbRnr_2_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].skk" maxlength="11" id="skk_2_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[1][6].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][6].kyStd" maxlength="10" id="kyStd_2_7" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_2_7" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].armNoTdrYc" maxlength="11" id="armNoTdrYc_2_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_2_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_2_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_2_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][6].armBoshJknNoGk" id="armBoshJknNoGk_2_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[1][7].bldCd" value="011278902">
                
                <tr>
                  <td><input type="text" name="rentRollList[1][7].msiNo" id="msiNo_2_8" value="8" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[1][7].sb" id="sb_2_8" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[1][7].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][7].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][7].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][7].madr" maxlength="8" id="madr_2_8" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].araM2" maxlength="8" id="araM2_2_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].araTb" id="araTb_2_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].mgNoRnr" maxlength="11" id="mgNoRnr_2_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].mgNoKeh" maxlength="11" id="mgNokeh_2_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].mgNoPk" maxlength="11" id="mgNoPk_2_8" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].mgNoGk" id="mgNoGk_2_8" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].tbRnr" id="tbRnr_2_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].skk" maxlength="11" id="skk_2_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[1][7].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][7].kyStd" maxlength="10" id="kyStd_2_8" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_2_8" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].armNoTdrYc" maxlength="11" id="armNoTdrYc_2_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_2_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_2_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_2_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][7].armBoshJknNoGk" id="armBoshJknNoGk_2_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[1][8].bldCd" value="011278902">
                
                <tr>
                  <td><input type="text" name="rentRollList[1][8].msiNo" id="msiNo_2_9" value="9" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[1][8].sb" id="sb_2_9" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[1][8].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][8].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][8].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][8].madr" maxlength="8" id="madr_2_9" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].araM2" maxlength="8" id="araM2_2_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].araTb" id="araTb_2_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].mgNoRnr" maxlength="11" id="mgNoRnr_2_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].mgNoKeh" maxlength="11" id="mgNokeh_2_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].mgNoPk" maxlength="11" id="mgNoPk_2_9" value="3,240" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].mgNoGk" id="mgNoGk_2_9" value="3,240" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].tbRnr" id="tbRnr_2_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].skk" maxlength="11" id="skk_2_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[1][8].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][8].kyStd" maxlength="10" id="kyStd_2_9" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_2_9" value="3,075" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].armNoTdrYc" maxlength="11" id="armNoTdrYc_2_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_2_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_2_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_2_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][8].armBoshJknNoGk" id="armBoshJknNoGk_2_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[1][9].bldCd" value="011278902">
                
                <tr>
                  <td><input type="text" name="rentRollList[1][9].msiNo" id="msiNo_2_10" value="10" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[1][9].sb" id="sb_2_10" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[1][9].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][9].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[1][9].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][9].madr" maxlength="8" id="madr_2_10" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].araM2" maxlength="8" id="araM2_2_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].araTb" id="araTb_2_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].mgNoRnr" maxlength="11" id="mgNoRnr_2_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].mgNoKeh" maxlength="11" id="mgNokeh_2_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].mgNoPk" maxlength="11" id="mgNoPk_2_10" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].mgNoGk" id="mgNoGk_2_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].tbRnr" id="tbRnr_2_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].skk" maxlength="11" id="skk_2_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[1][9].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[1][9].kyStd" maxlength="10" id="kyStd_2_10" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_2_10" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].armNoTdrYc" maxlength="11" id="armNoTdrYc_2_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_2_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_2_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_2_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[1][9].armBoshJknNoGk" id="armBoshJknNoGk_2_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[2][0].bldCd" value="011278903">
                
                <tr>
                  <td class="output-rentroll-first-row" colspan="24">エム・○ル○エ○ (建物CD：011278903)</td>
                </tr>
                
                <tr>
                  <td><input type="text" name="rentRollList[2][0].msiNo" id="msiNo_3_1" value="1" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[2][0].sb" id="sb_3_1" value="101" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[2][0].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中" selected="">入居中</option>
                            <option value="募集中">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][0].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][0].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][0].madr" maxlength="8" id="madr_3_1" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].araM2" maxlength="8" id="araM2_3_1" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].araTb" id="araTb_3_1" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].mgNoRnr" maxlength="11" id="mgNoRnr_3_1" value="64,000" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].mgNoKeh" maxlength="11" id="mgNokeh_3_1" value="4,700" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].mgNoPk" maxlength="11" id="mgNoPk_3_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].mgNoGk" id="mgNoGk_3_1" value="68,700" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].tbRnr" id="tbRnr_3_1" value="3,903" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].skk" maxlength="11" id="skk_3_1" value="192,000" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[2][0].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有">有</option>
                            <option value="無" selected="">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][0].kyStd" maxlength="10" id="kyStd_3_1" value="2002/10/31" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_3_1" value="57,920" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].armNoTdrYc" maxlength="11" id="armNoTdrYc_3_1" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_3_1" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_3_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_3_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][0].armBoshJknNoGk" id="armBoshJknNoGk_3_1" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[2][1].bldCd" value="011278903">
                
                <tr>
                  <td><input type="text" name="rentRollList[2][1].msiNo" id="msiNo_3_2" value="2" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[2][1].sb" id="sb_3_2" value="102" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[2][1].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中" selected="">入居中</option>
                            <option value="募集中">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][1].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][1].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][1].madr" maxlength="8" id="madr_3_2" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].araM2" maxlength="8" id="araM2_3_2" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].araTb" id="araTb_3_2" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].mgNoRnr" maxlength="11" id="mgNoRnr_3_2" value="44,500" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].mgNoKeh" maxlength="11" id="mgNokeh_3_2" value="4,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].mgNoPk" maxlength="11" id="mgNoPk_3_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].mgNoGk" id="mgNoGk_3_2" value="49,000" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].tbRnr" id="tbRnr_3_2" value="2,784" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].skk" maxlength="11" id="skk_3_2" value="0" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[2][1].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有" selected="">有</option>
                            <option value="無">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][1].kyStd" maxlength="10" id="kyStd_3_2" value="2019/12/01" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_3_2" value="40,273" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].armNoTdrYc" maxlength="11" id="armNoTdrYc_3_2" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_3_2" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_3_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_3_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][1].armBoshJknNoGk" id="armBoshJknNoGk_3_2" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[2][2].bldCd" value="011278903">
                
                <tr>
                  <td><input type="text" name="rentRollList[2][2].msiNo" id="msiNo_3_3" value="3" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[2][2].sb" id="sb_3_3" value="103" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[2][2].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中" selected="">入居中</option>
                            <option value="募集中">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][2].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][2].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][2].madr" maxlength="8" id="madr_3_3" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].araM2" maxlength="8" id="araM2_3_3" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].araTb" id="araTb_3_3" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].mgNoRnr" maxlength="11" id="mgNoRnr_3_3" value="50,000" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].mgNoKeh" maxlength="11" id="mgNokeh_3_3" value="4,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].mgNoPk" maxlength="11" id="mgNoPk_3_3" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].mgNoGk" id="mgNoGk_3_3" value="54,500" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].tbRnr" id="tbRnr_3_3" value="3,097" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].skk" maxlength="11" id="skk_3_3" value="100,000" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[2][2].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有">有</option>
                            <option value="無" selected="">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][2].kyStd" maxlength="10" id="kyStd_3_3" value="2012/08/01" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_3_3" value="45,250" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].armNoTdrYc" maxlength="11" id="armNoTdrYc_3_3" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_3_3" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_3_3" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_3_3" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][2].armBoshJknNoGk" id="armBoshJknNoGk_3_3" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[2][3].bldCd" value="011278903">
                
                <tr>
                  <td><input type="text" name="rentRollList[2][3].msiNo" id="msiNo_3_4" value="4" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[2][3].sb" id="sb_3_4" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[2][3].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][3].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][3].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][3].madr" maxlength="8" id="madr_3_4" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].araM2" maxlength="8" id="araM2_3_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].araTb" id="araTb_3_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].mgNoRnr" maxlength="11" id="mgNoRnr_3_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].mgNoKeh" maxlength="11" id="mgNokeh_3_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].mgNoPk" maxlength="11" id="mgNoPk_3_4" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].mgNoGk" id="mgNoGk_3_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].tbRnr" id="tbRnr_3_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].skk" maxlength="11" id="skk_3_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[2][3].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][3].kyStd" maxlength="10" id="kyStd_3_4" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_3_4" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].armNoTdrYc" maxlength="11" id="armNoTdrYc_3_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_3_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_3_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_3_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][3].armBoshJknNoGk" id="armBoshJknNoGk_3_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[2][4].bldCd" value="011278903">
                
                <tr>
                  <td><input type="text" name="rentRollList[2][4].msiNo" id="msiNo_3_5" value="5" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[2][4].sb" id="sb_3_5" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[2][4].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][4].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][4].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][4].madr" maxlength="8" id="madr_3_5" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].araM2" maxlength="8" id="araM2_3_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].araTb" id="araTb_3_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].mgNoRnr" maxlength="11" id="mgNoRnr_3_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].mgNoKeh" maxlength="11" id="mgNokeh_3_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].mgNoPk" maxlength="11" id="mgNoPk_3_5" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].mgNoGk" id="mgNoGk_3_5" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].tbRnr" id="tbRnr_3_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].skk" maxlength="11" id="skk_3_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[2][4].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][4].kyStd" maxlength="10" id="kyStd_3_5" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_3_5" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].armNoTdrYc" maxlength="11" id="armNoTdrYc_3_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_3_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_3_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_3_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][4].armBoshJknNoGk" id="armBoshJknNoGk_3_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[2][5].bldCd" value="011278903">
                
                <tr>
                  <td><input type="text" name="rentRollList[2][5].msiNo" id="msiNo_3_6" value="6" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[2][5].sb" id="sb_3_6" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[2][5].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][5].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][5].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][5].madr" maxlength="8" id="madr_3_6" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].araM2" maxlength="8" id="araM2_3_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].araTb" id="araTb_3_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].mgNoRnr" maxlength="11" id="mgNoRnr_3_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].mgNoKeh" maxlength="11" id="mgNokeh_3_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].mgNoPk" maxlength="11" id="mgNoPk_3_6" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].mgNoGk" id="mgNoGk_3_6" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].tbRnr" id="tbRnr_3_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].skk" maxlength="11" id="skk_3_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[2][5].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][5].kyStd" maxlength="10" id="kyStd_3_6" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_3_6" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].armNoTdrYc" maxlength="11" id="armNoTdrYc_3_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_3_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_3_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_3_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][5].armBoshJknNoGk" id="armBoshJknNoGk_3_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[2][6].bldCd" value="011278903">
                
                <tr>
                  <td><input type="text" name="rentRollList[2][6].msiNo" id="msiNo_3_7" value="7" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[2][6].sb" id="sb_3_7" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[2][6].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][6].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][6].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][6].madr" maxlength="8" id="madr_3_7" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].araM2" maxlength="8" id="araM2_3_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].araTb" id="araTb_3_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].mgNoRnr" maxlength="11" id="mgNoRnr_3_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].mgNoKeh" maxlength="11" id="mgNokeh_3_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].mgNoPk" maxlength="11" id="mgNoPk_3_7" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].mgNoGk" id="mgNoGk_3_7" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].tbRnr" id="tbRnr_3_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].skk" maxlength="11" id="skk_3_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[2][6].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][6].kyStd" maxlength="10" id="kyStd_3_7" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_3_7" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].armNoTdrYc" maxlength="11" id="armNoTdrYc_3_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_3_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_3_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_3_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][6].armBoshJknNoGk" id="armBoshJknNoGk_3_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[2][7].bldCd" value="011278903">
                
                <tr>
                  <td><input type="text" name="rentRollList[2][7].msiNo" id="msiNo_3_8" value="8" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[2][7].sb" id="sb_3_8" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[2][7].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][7].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][7].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][7].madr" maxlength="8" id="madr_3_8" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].araM2" maxlength="8" id="araM2_3_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].araTb" id="araTb_3_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].mgNoRnr" maxlength="11" id="mgNoRnr_3_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].mgNoKeh" maxlength="11" id="mgNokeh_3_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].mgNoPk" maxlength="11" id="mgNoPk_3_8" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].mgNoGk" id="mgNoGk_3_8" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].tbRnr" id="tbRnr_3_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].skk" maxlength="11" id="skk_3_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[2][7].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][7].kyStd" maxlength="10" id="kyStd_3_8" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_3_8" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].armNoTdrYc" maxlength="11" id="armNoTdrYc_3_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_3_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_3_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_3_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][7].armBoshJknNoGk" id="armBoshJknNoGk_3_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[2][8].bldCd" value="011278903">
                
                <tr>
                  <td><input type="text" name="rentRollList[2][8].msiNo" id="msiNo_3_9" value="9" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[2][8].sb" id="sb_3_9" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[2][8].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][8].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][8].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][8].madr" maxlength="8" id="madr_3_9" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].araM2" maxlength="8" id="araM2_3_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].araTb" id="araTb_3_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].mgNoRnr" maxlength="11" id="mgNoRnr_3_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].mgNoKeh" maxlength="11" id="mgNokeh_3_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].mgNoPk" maxlength="11" id="mgNoPk_3_9" value="3,150" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].mgNoGk" id="mgNoGk_3_9" value="3,150" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].tbRnr" id="tbRnr_3_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].skk" maxlength="11" id="skk_3_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[2][8].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][8].kyStd" maxlength="10" id="kyStd_3_9" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_3_9" value="2,985" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].armNoTdrYc" maxlength="11" id="armNoTdrYc_3_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_3_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_3_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_3_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][8].armBoshJknNoGk" id="armBoshJknNoGk_3_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[2][9].bldCd" value="011278903">
                
                <tr>
                  <td><input type="text" name="rentRollList[2][9].msiNo" id="msiNo_3_10" value="10" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[2][9].sb" id="sb_3_10" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[2][9].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][9].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[2][9].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][9].madr" maxlength="8" id="madr_3_10" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].araM2" maxlength="8" id="araM2_3_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].araTb" id="araTb_3_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].mgNoRnr" maxlength="11" id="mgNoRnr_3_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].mgNoKeh" maxlength="11" id="mgNokeh_3_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].mgNoPk" maxlength="11" id="mgNoPk_3_10" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].mgNoGk" id="mgNoGk_3_10" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].tbRnr" id="tbRnr_3_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].skk" maxlength="11" id="skk_3_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[2][9].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[2][9].kyStd" maxlength="10" id="kyStd_3_10" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_3_10" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].armNoTdrYc" maxlength="11" id="armNoTdrYc_3_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_3_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_3_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_3_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[2][9].armBoshJknNoGk" id="armBoshJknNoGk_3_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][0].bldCd" value="011278904">
                
                <tr>
                  <td class="output-rentroll-first-row" colspan="24">エム・○ル○エ○ (建物CD：011278904)</td>
                </tr>
                
                <tr>
                  <td><input type="text" name="rentRollList[3][0].msiNo" id="msiNo_4_1" value="1" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][0].sb" id="sb_4_1" value="101" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][0].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中">入居中</option>
                            <option value="募集中" selected="">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][0].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][0].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][0].madr" maxlength="8" id="madr_4_1" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].araM2" maxlength="8" id="araM2_4_1" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].araTb" id="araTb_4_1" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].mgNoRnr" maxlength="11" id="mgNoRnr_4_1" value="45,500" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].mgNoKeh" maxlength="11" id="mgNokeh_4_1" value="4,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].mgNoPk" maxlength="11" id="mgNoPk_4_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].mgNoGk" id="mgNoGk_4_1" value="50,000" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].tbRnr" id="tbRnr_4_1" value="2,841" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].skk" maxlength="11" id="skk_4_1" value="0" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[3][0].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有">有</option>
                            <option value="無" selected="">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][0].kyStd" maxlength="10" id="kyStd_4_1" value="" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_1" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_1" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_1" value="45,500" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_1" value="4,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_1" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][0].armBoshJknNoGk" id="armBoshJknNoGk_4_1" value="50,000" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][1].bldCd" value="011278904">
                
                <tr>
                  <td><input type="text" name="rentRollList[3][1].msiNo" id="msiNo_4_2" value="2" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][1].sb" id="sb_4_2" value="102" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][1].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中">入居中</option>
                            <option value="募集中" selected="">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][1].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][1].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][1].madr" maxlength="8" id="madr_4_2" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].araM2" maxlength="8" id="araM2_4_2" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].araTb" id="araTb_4_2" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].mgNoRnr" maxlength="11" id="mgNoRnr_4_2" value="44,500" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].mgNoKeh" maxlength="11" id="mgNokeh_4_2" value="4,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].mgNoPk" maxlength="11" id="mgNoPk_4_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].mgNoGk" id="mgNoGk_4_2" value="49,000" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].tbRnr" id="tbRnr_4_2" value="2,784" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].skk" maxlength="11" id="skk_4_2" value="0" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[3][1].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有">有</option>
                            <option value="無" selected="">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][1].kyStd" maxlength="10" id="kyStd_4_2" value="" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_2" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_2" value="6,570" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_2" value="44,500" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_2" value="4,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_2" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][1].armBoshJknNoGk" id="armBoshJknNoGk_4_2" value="49,000" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][2].bldCd" value="011278904">
                
                <tr>
                  <td><input type="text" name="rentRollList[3][2].msiNo" id="msiNo_4_3" value="3" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][2].sb" id="sb_4_3" value="103" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][2].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="入居中" selected="">入居中</option>
                            <option value="募集中">募集中</option>
                            <option value="退居予定">退居予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][2].kym" class="output-rentroll-60">
                            <option value="">-</option>
                            <option value="個人">個人</option>
                            <option value="法人">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][2].yot" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="居住用" selected="">居住用</option>
                            <option value="事業用">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][2].madr" maxlength="8" id="madr_4_3" value="2LDK" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].araM2" maxlength="8" id="araM2_4_3" value="58.17" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].araTb" id="araTb_4_3" value="17.6" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].mgNoRnr" maxlength="11" id="mgNoRnr_4_3" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].mgNoKeh" maxlength="11" id="mgNokeh_4_3" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].mgNoPk" maxlength="11" id="mgNoPk_4_3" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].mgNoGk" id="mgNoGk_4_3" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].tbRnr" id="tbRnr_4_3" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].skk" maxlength="11" id="skk_4_3" value="0" class="output-rentroll-60">
                  </td>
                    <td>
                        <select name="rentRollList[3][2].hshCmp" class="output-rentroll-70">
                            <option value="">-</option>
                            <option value="有" selected="">有</option>
                            <option value="無">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][2].kyStd" maxlength="10" id="kyStd_4_3" value="2019/09/28" class="output-rentroll-70">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_3" value="41,178" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_3" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_3" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_3" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_3" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][2].armBoshJknNoGk" id="armBoshJknNoGk_4_3" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][3].bldCd" value="011278904">
                
                <tr>
                  <td><input type="text" name="rentRollList[3][3].msiNo" id="msiNo_4_4" value="4" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][3].sb" id="sb_4_4" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][3].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][3].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][3].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][3].madr" maxlength="8" id="madr_4_4" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].araM2" maxlength="8" id="araM2_4_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].araTb" id="araTb_4_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].mgNoRnr" maxlength="11" id="mgNoRnr_4_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].mgNoKeh" maxlength="11" id="mgNokeh_4_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].mgNoPk" maxlength="11" id="mgNoPk_4_4" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].mgNoGk" id="mgNoGk_4_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].tbRnr" id="tbRnr_4_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].skk" maxlength="11" id="skk_4_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[3][3].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][3].kyStd" maxlength="10" id="kyStd_4_4" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_4" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_4" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][3].armBoshJknNoGk" id="armBoshJknNoGk_4_4" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][4].bldCd" value="011278904">
                
                <tr>
                  <td><input type="text" name="rentRollList[3][4].msiNo" id="msiNo_4_5" value="5" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][4].sb" id="sb_4_5" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][4].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][4].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][4].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][4].madr" maxlength="8" id="madr_4_5" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].araM2" maxlength="8" id="araM2_4_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].araTb" id="araTb_4_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].mgNoRnr" maxlength="11" id="mgNoRnr_4_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].mgNoKeh" maxlength="11" id="mgNokeh_4_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].mgNoPk" maxlength="11" id="mgNoPk_4_5" value="0" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].mgNoGk" id="mgNoGk_4_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].tbRnr" id="tbRnr_4_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].skk" maxlength="11" id="skk_4_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[3][4].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][4].kyStd" maxlength="10" id="kyStd_4_5" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_5" value="0" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_5" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][4].armBoshJknNoGk" id="armBoshJknNoGk_4_5" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][5].bldCd" value="011278904">
                
                <tr>
                  <td><input type="text" name="rentRollList[3][5].msiNo" id="msiNo_4_6" value="6" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][5].sb" id="sb_4_6" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][5].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][5].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][5].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][5].madr" maxlength="8" id="madr_4_6" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].araM2" maxlength="8" id="araM2_4_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].araTb" id="araTb_4_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].mgNoRnr" maxlength="11" id="mgNoRnr_4_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].mgNoKeh" maxlength="11" id="mgNokeh_4_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].mgNoPk" maxlength="11" id="mgNoPk_4_6" value="3,240" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].mgNoGk" id="mgNoGk_4_6" value="3,240" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].tbRnr" id="tbRnr_4_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].skk" maxlength="11" id="skk_4_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[3][5].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][5].kyStd" maxlength="10" id="kyStd_4_6" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_6" value="3,075" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_6" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][5].armBoshJknNoGk" id="armBoshJknNoGk_4_6" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][6].bldCd" value="011278904">
                
                <tr>
                  <td><input type="text" name="rentRollList[3][6].msiNo" id="msiNo_4_7" value="7" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][6].sb" id="sb_4_7" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][6].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][6].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][6].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][6].madr" maxlength="8" id="madr_4_7" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].araM2" maxlength="8" id="araM2_4_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].araTb" id="araTb_4_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].mgNoRnr" maxlength="11" id="mgNoRnr_4_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].mgNoKeh" maxlength="11" id="mgNokeh_4_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].mgNoPk" maxlength="11" id="mgNoPk_4_7" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].mgNoGk" id="mgNoGk_4_7" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].tbRnr" id="tbRnr_4_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].skk" maxlength="11" id="skk_4_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[3][6].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][6].kyStd" maxlength="10" id="kyStd_4_7" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_7" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_7" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][6].armBoshJknNoGk" id="armBoshJknNoGk_4_7" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][7].bldCd" value="011278904">
                
                <tr>
                  <td><input type="text" name="rentRollList[3][7].msiNo" id="msiNo_4_8" value="8" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][7].sb" id="sb_4_8" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][7].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][7].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][7].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][7].madr" maxlength="8" id="madr_4_8" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].araM2" maxlength="8" id="araM2_4_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].araTb" id="araTb_4_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].mgNoRnr" maxlength="11" id="mgNoRnr_4_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].mgNoKeh" maxlength="11" id="mgNokeh_4_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].mgNoPk" maxlength="11" id="mgNoPk_4_8" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].mgNoGk" id="mgNoGk_4_8" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].tbRnr" id="tbRnr_4_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].skk" maxlength="11" id="skk_4_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[3][7].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][7].kyStd" maxlength="10" id="kyStd_4_8" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_8" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_8" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][7].armBoshJknNoGk" id="armBoshJknNoGk_4_8" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][8].bldCd" value="011278904">
                
                <tr>
                  <td><input type="text" name="rentRollList[3][8].msiNo" id="msiNo_4_9" value="9" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][8].sb" id="sb_4_9" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][8].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][8].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][8].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][8].madr" maxlength="8" id="madr_4_9" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].araM2" maxlength="8" id="araM2_4_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].araTb" id="araTb_4_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].mgNoRnr" maxlength="11" id="mgNoRnr_4_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].mgNoKeh" maxlength="11" id="mgNokeh_4_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].mgNoPk" maxlength="11" id="mgNoPk_4_9" value="5,500" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].mgNoGk" id="mgNoGk_4_9" value="5,500" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].tbRnr" id="tbRnr_4_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].skk" maxlength="11" id="skk_4_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[3][8].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][8].kyStd" maxlength="10" id="kyStd_4_9" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_9" value="5,225" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_9" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][8].armBoshJknNoGk" id="armBoshJknNoGk_4_9" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][9].bldCd" value="011278904">
                
                <tr>
                  <td><input type="text" name="rentRollList[3][9].msiNo" id="msiNo_4_10" value="10" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][9].sb" id="sb_4_10" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][9].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][9].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][9].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][9].madr" maxlength="8" id="madr_4_10" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].araM2" maxlength="8" id="araM2_4_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].araTb" id="araTb_4_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].mgNoRnr" maxlength="11" id="mgNoRnr_4_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].mgNoKeh" maxlength="11" id="mgNokeh_4_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].mgNoPk" maxlength="11" id="mgNoPk_4_10" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].mgNoGk" id="mgNoGk_4_10" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].tbRnr" id="tbRnr_4_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].skk" maxlength="11" id="skk_4_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[3][9].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][9].kyStd" maxlength="10" id="kyStd_4_10" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_10" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_10" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][9].armBoshJknNoGk" id="armBoshJknNoGk_4_10" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
           
                <input type="hidden" name="rentRollList[3][10].bldCd" value="011278904">
                
                <tr>
                  <td><input type="text" name="rentRollList[3][10].msiNo" id="msiNo_4_11" value="11" class="output-rentroll-20" readonly=""></td>
                  <td><input type="text" name="rentRollList[3][10].sb" id="sb_4_11" value="駐車場" class="output-rentroll-40" readonly=""></td>
                    <td>
                        <select name="rentRollList[3][10].jok" class="output-rentroll-80">
                            <option value="">-</option>
            
                            <option value="使用中" selected="">使用中</option>
                            <option value="募集中">募集中</option>
                            <option value="空予定">空予定</option>
                
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][10].kym" class="output-rentroll-60" readonly="">
                            <option value="" disabled="disabled">-</option>
                            <option value="個人" selected="">個人</option>
                            <option value="法人" disabled="disabled">法人</option>
                        </select>
                    </td>
                    <td>
                        <select name="rentRollList[3][10].yot" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="居住用" disabled="disabled">居住用</option>
                            <option value="事業用" disabled="disabled">事業用</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][10].madr" maxlength="8" id="madr_4_11" value="" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].araM2" maxlength="8" id="araM2_4_11" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].araTb" id="araTb_4_11" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].mgNoRnr" maxlength="11" id="mgNoRnr_4_11" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].mgNoKeh" maxlength="11" id="mgNokeh_4_11" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].mgNoPk" maxlength="11" id="mgNoPk_4_11" value="3,300" class="output-rentroll-40">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].mgNoGk" id="mgNoGk_4_11" value="3,300" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].tbRnr" id="tbRnr_4_11" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].skk" maxlength="11" id="skk_4_11" value="0" class="output-rentroll-60" readonly="">
                  </td>
                    <td>
                        <select name="rentRollList[3][10].hshCmp" class="output-rentroll-70" readonly="">
                            <option value="">-</option>
                            <option value="有" disabled="disabled">有</option>
                            <option value="無" disabled="disabled">無</option>
                        </select>
                    </td>
                  <td><input type="text" name="rentRollList[3][10].kyStd" maxlength="10" id="kyStd_4_11" value="" class="output-rentroll-70" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].nkcNoTdrYc" maxlength="11" id="nkcNoTdrYc_4_11" value="3,135" class="output-rentroll-60">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].armNoTdrYc" maxlength="11" id="armNoTdrYc_4_11" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].armBoshJknNoRnr" maxlength="11" id="armBoshJknNoRnr_4_11" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].armBoshJknNoKeh" maxlength="11" id="armBoshJknNoKeh_4_11" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].armBoshJknNoPk" maxlength="11" id="armBoshJknNoPk_4_11" value="0" class="output-rentroll-40" readonly="">
                  </td>
                  <td><input type="text" name="rentRollList[3][10].armBoshJknNoGk" id="armBoshJknNoGk_4_11" value="0" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
          
                <tr>
                  <td colspan="6">合計</td>
                  <td><input type="text" value="639.87" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="193.6" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="478,500" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="45,400" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="69,880" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="593,780" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="29,766" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="410,000" class="output-rentroll-60" readonly="">
                  </td>
                  <td></td>
                  <td></td>
                  <td><input type="text" value="443,746" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="6,570" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="90,000" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="9,000" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="0" class="output-rentroll-60" readonly="">
                  </td>
                  <td><input type="text" value="99,000" class="output-rentroll-60" readonly="">
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr>
          <td>
            <table border="0" align="left">
              <tbody>
                <tr>
                  <td align="left" valign="top">
                    <table border="1" valign="top">
                      <tbody>
                        <tr>
                          <td colspan="2">リーシング使用欄</td>
                        </tr>
                        <tr>
                          <td>表面利回り</td>
                          <td><input type="text" name="totalItem.listRmw" id="listRmw" value="0.00" class="output-rentroll-100" readonly="">％</td>
                        </tr>
                        <tr>
                          <td>実質利回り</td>
                          <td><input type="text" name="totalItem.jstRmw" id="jstRmw" value="0.00" class="output-rentroll-100" readonly="">％</td>
                        </tr>
                        <tr>
                          <td>販売価格</td>
                          <td><input type="text" name="totalItem.slpr" maxlength="11" id="slpr" value="0" class="output-rentroll-100">
                          </td>
                        </tr>
                        <tr>
                          <td>担当店</td>
                          <td><input type="text" name="totalItem.ttTn" maxlength="10" id="ttTn" value="" class="output-rentroll">
                          </td>
                        </tr>
                        <tr>
                          <td>担当者</td>
                          <td><input type="text" name="totalItem.ttm" maxlength="10" id="ttm" value="" class="output-rentroll">
                          </td>
                        </tr>
                        <tr>
                          <td>連絡先</td>
                          <td><input type="text" name="totalItem.rrs" maxlength="15" id="rrs" value="" class="output-rentroll">
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                  <td align="left" valign="top">
                    <table border="1" valign="top" align="left">
                      <thead class="rentroll-table-thead">
                        <tr>
                          <td class="thead-rentroll-173">支出項目（月額）</td>
                          <td class="thead-rentroll-104">金額</td>
                          <td class="thead-rentroll-173">収入項目（月額）</td>
                          <td class="thead-rentroll-104">金額</td>
                        </tr>
                        </thead>
                      <tbody class="expenditure-table-tbody">
                        
                            <input type="hidden" name="expenditureItemList[0][0].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][0].msiNo" value="1">
                            <input type="hidden" name="incomeItemList[0][0].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][0].msiNo" value="1">
                            
                                  <tr>
                                    <td class="output-rentroll-first-row" colspan="4">エム・○ル○エ○ (建物CD：011278901)</td>
                                  </tr>
                                
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][0].kmm" maxlength="20" id="ssht_kmm_1_1" value="原状回復調整金" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][0].kg" maxlength="14" id="ssht_kg_1_1" value="1,848" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][0].kmm" maxlength="20" id="icm_kmm_1_1" value="支払済賃料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][0].kg" maxlength="14" id="icm_kg_1_1" value="300" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][1].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][1].msiNo" value="2">
                            <input type="hidden" name="incomeItemList[0][1].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][1].msiNo" value="2">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][1].kmm" maxlength="20" id="ssht_kmm_1_2" value="オーナーズＧ保険料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][1].kg" maxlength="14" id="ssht_kg_1_2" value="552" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][1].kmm" maxlength="20" id="icm_kmm_1_2" value="一般回収" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][1].kg" maxlength="14" id="icm_kg_1_2" value="400" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][2].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][2].msiNo" value="3">
                            <input type="hidden" name="incomeItemList[0][2].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][2].msiNo" value="3">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][2].kmm" maxlength="20" id="ssht_kmm_1_3" value="ワランティサービス" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][2].kg" maxlength="14" id="ssht_kg_1_3" value="4,180" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][2].kmm" maxlength="20" id="icm_kmm_1_3" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][2].kg" maxlength="14" id="icm_kg_1_3" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][3].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][3].msiNo" value="4">
                            <input type="hidden" name="incomeItemList[0][3].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][3].msiNo" value="4">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][3].kmm" maxlength="20" id="ssht_kmm_1_4" value="支出１" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][3].kg" maxlength="14" id="ssht_kg_1_4" value="100" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][3].kmm" maxlength="20" id="icm_kmm_1_4" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][3].kg" maxlength="14" id="icm_kg_1_4" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][4].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][4].msiNo" value="5">
                            <input type="hidden" name="incomeItemList[0][4].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][4].msiNo" value="5">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][4].kmm" maxlength="20" id="ssht_kmm_1_5" value="支出２" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][4].kg" maxlength="14" id="ssht_kg_1_5" value="500" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][4].kmm" maxlength="20" id="icm_kmm_1_5" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][4].kg" maxlength="14" id="icm_kg_1_5" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][5].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][5].msiNo" value="6">
                            <input type="hidden" name="incomeItemList[0][5].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][5].msiNo" value="6">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][5].kmm" maxlength="20" id="ssht_kmm_1_6" value="支出３" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][5].kg" maxlength="14" id="ssht_kg_1_6" value="300" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][5].kmm" maxlength="20" id="icm_kmm_1_6" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][5].kg" maxlength="14" id="icm_kg_1_6" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][6].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][6].msiNo" value="7">
                            <input type="hidden" name="incomeItemList[0][6].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][6].msiNo" value="7">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][6].kmm" maxlength="20" id="ssht_kmm_1_7" value="支出４" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][6].kg" maxlength="14" id="ssht_kg_1_7" value="400" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][6].kmm" maxlength="20" id="icm_kmm_1_7" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][6].kg" maxlength="14" id="icm_kg_1_7" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][7].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][7].msiNo" value="8">
                            <input type="hidden" name="incomeItemList[0][7].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][7].msiNo" value="8">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][7].kmm" maxlength="20" id="ssht_kmm_1_8" value="支出５" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][7].kg" maxlength="14" id="ssht_kg_1_8" value="500" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][7].kmm" maxlength="20" id="icm_kmm_1_8" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][7].kg" maxlength="14" id="icm_kg_1_8" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][8].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][8].msiNo" value="9">
                            <input type="hidden" name="incomeItemList[0][8].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][8].msiNo" value="9">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][8].kmm" maxlength="20" id="ssht_kmm_1_9" value="支出６" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][8].kg" maxlength="14" id="ssht_kg_1_9" value="600" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][8].kmm" maxlength="20" id="icm_kmm_1_9" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][8].kg" maxlength="14" id="icm_kg_1_9" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][9].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][9].msiNo" value="10">
                            <input type="hidden" name="incomeItemList[0][9].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][9].msiNo" value="10">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][9].kmm" maxlength="20" id="ssht_kmm_1_10" value="支出７" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][9].kg" maxlength="14" id="ssht_kg_1_10" value="700" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][9].kmm" maxlength="20" id="icm_kmm_1_10" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][9].kg" maxlength="14" id="icm_kg_1_10" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][10].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][10].msiNo" value="11">
                            <input type="hidden" name="incomeItemList[0][10].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][10].msiNo" value="11">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][10].kmm" maxlength="20" id="ssht_kmm_1_11" value="支出８" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][10].kg" maxlength="14" id="ssht_kg_1_11" value="800" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][10].kmm" maxlength="20" id="icm_kmm_1_11" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][10].kg" maxlength="14" id="icm_kg_1_11" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][11].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][11].msiNo" value="12">
                            <input type="hidden" name="incomeItemList[0][11].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][11].msiNo" value="12">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][11].kmm" maxlength="20" id="ssht_kmm_1_12" value="支出９" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][11].kg" maxlength="14" id="ssht_kg_1_12" value="900" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][11].kmm" maxlength="20" id="icm_kmm_1_12" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][11].kg" maxlength="14" id="icm_kg_1_12" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][12].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][12].msiNo" value="13">
                            <input type="hidden" name="incomeItemList[0][12].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][12].msiNo" value="13">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][12].kmm" maxlength="20" id="ssht_kmm_1_13" value="支出１０" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][12].kg" maxlength="14" id="ssht_kg_1_13" value="1,000" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][12].kmm" maxlength="20" id="icm_kmm_1_13" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][12].kg" maxlength="14" id="icm_kg_1_13" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][13].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][13].msiNo" value="14">
                            <input type="hidden" name="incomeItemList[0][13].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][13].msiNo" value="14">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][13].kmm" maxlength="20" id="ssht_kmm_1_14" value="支出１１" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][13].kg" maxlength="14" id="ssht_kg_1_14" value="1,100" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][13].kmm" maxlength="20" id="icm_kmm_1_14" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][13].kg" maxlength="14" id="icm_kg_1_14" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][14].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][14].msiNo" value="15">
                            <input type="hidden" name="incomeItemList[0][14].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][14].msiNo" value="15">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][14].kmm" maxlength="20" id="ssht_kmm_1_15" value="支出１２" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][14].kg" maxlength="14" id="ssht_kg_1_15" value="1,200" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][14].kmm" maxlength="20" id="icm_kmm_1_15" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][14].kg" maxlength="14" id="icm_kg_1_15" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][15].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][15].msiNo" value="16">
                            <input type="hidden" name="incomeItemList[0][15].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][15].msiNo" value="16">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][15].kmm" maxlength="20" id="ssht_kmm_1_16" value="支出１３" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][15].kg" maxlength="14" id="ssht_kg_1_16" value="1,300" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][15].kmm" maxlength="20" id="icm_kmm_1_16" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][15].kg" maxlength="14" id="icm_kg_1_16" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][16].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][16].msiNo" value="17">
                            <input type="hidden" name="incomeItemList[0][16].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][16].msiNo" value="17">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][16].kmm" maxlength="20" id="ssht_kmm_1_17" value="支出１４" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][16].kg" maxlength="14" id="ssht_kg_1_17" value="1,400" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][16].kmm" maxlength="20" id="icm_kmm_1_17" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][16].kg" maxlength="14" id="icm_kg_1_17" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][17].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][17].msiNo" value="18">
                            <input type="hidden" name="incomeItemList[0][17].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][17].msiNo" value="18">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][17].kmm" maxlength="20" id="ssht_kmm_1_18" value="支出１５" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][17].kg" maxlength="14" id="ssht_kg_1_18" value="1,500" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][17].kmm" maxlength="20" id="icm_kmm_1_18" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][17].kg" maxlength="14" id="icm_kg_1_18" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][18].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][18].msiNo" value="19">
                            <input type="hidden" name="incomeItemList[0][18].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][18].msiNo" value="19">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][18].kmm" maxlength="20" id="ssht_kmm_1_19" value="支出１６" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][18].kg" maxlength="14" id="ssht_kg_1_19" value="1,600" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][18].kmm" maxlength="20" id="icm_kmm_1_19" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][18].kg" maxlength="14" id="icm_kg_1_19" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][19].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][19].msiNo" value="20">
                            <input type="hidden" name="incomeItemList[0][19].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][19].msiNo" value="20">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][19].kmm" maxlength="20" id="ssht_kmm_1_20" value="支出１７" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][19].kg" maxlength="14" id="ssht_kg_1_20" value="1,700" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][19].kmm" maxlength="20" id="icm_kmm_1_20" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][19].kg" maxlength="14" id="icm_kg_1_20" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][20].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][20].msiNo" value="21">
                            <input type="hidden" name="incomeItemList[0][20].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][20].msiNo" value="21">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][20].kmm" maxlength="20" id="ssht_kmm_1_21" value="支出１８" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][20].kg" maxlength="14" id="ssht_kg_1_21" value="1,800" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][20].kmm" maxlength="20" id="icm_kmm_1_21" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][20].kg" maxlength="14" id="icm_kg_1_21" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][21].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][21].msiNo" value="22">
                            <input type="hidden" name="incomeItemList[0][21].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][21].msiNo" value="22">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][21].kmm" maxlength="20" id="ssht_kmm_1_22" value="支出１９" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][21].kg" maxlength="14" id="ssht_kg_1_22" value="1,900" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][21].kmm" maxlength="20" id="icm_kmm_1_22" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][21].kg" maxlength="14" id="icm_kg_1_22" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][22].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][22].msiNo" value="23">
                            <input type="hidden" name="incomeItemList[0][22].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][22].msiNo" value="23">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][22].kmm" maxlength="20" id="ssht_kmm_1_23" value="支出２０" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][22].kg" maxlength="14" id="ssht_kg_1_23" value="2,000" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][22].kmm" maxlength="20" id="icm_kmm_1_23" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][22].kg" maxlength="14" id="icm_kg_1_23" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][23].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][23].msiNo" value="24">
                            <input type="hidden" name="incomeItemList[0][23].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][23].msiNo" value="24">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][23].kmm" maxlength="20" id="ssht_kmm_1_24" value="支出２１" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][23].kg" maxlength="14" id="ssht_kg_1_24" value="2,100" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][23].kmm" maxlength="20" id="icm_kmm_1_24" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][23].kg" maxlength="14" id="icm_kg_1_24" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][24].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][24].msiNo" value="25">
                            <input type="hidden" name="incomeItemList[0][24].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][24].msiNo" value="25">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][24].kmm" maxlength="20" id="ssht_kmm_1_25" value="支出２２" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][24].kg" maxlength="14" id="ssht_kg_1_25" value="2,200" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][24].kmm" maxlength="20" id="icm_kmm_1_25" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][24].kg" maxlength="14" id="icm_kg_1_25" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][25].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][25].msiNo" value="26">
                            <input type="hidden" name="incomeItemList[0][25].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][25].msiNo" value="26">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][25].kmm" maxlength="20" id="ssht_kmm_1_26" value="支出２３" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][25].kg" maxlength="14" id="ssht_kg_1_26" value="2,300" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][25].kmm" maxlength="20" id="icm_kmm_1_26" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][25].kg" maxlength="14" id="icm_kg_1_26" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][26].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][26].msiNo" value="27">
                            <input type="hidden" name="incomeItemList[0][26].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][26].msiNo" value="27">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][26].kmm" maxlength="20" id="ssht_kmm_1_27" value="支出２４" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][26].kg" maxlength="14" id="ssht_kg_1_27" value="2,400" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][26].kmm" maxlength="20" id="icm_kmm_1_27" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][26].kg" maxlength="14" id="icm_kg_1_27" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][27].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][27].msiNo" value="28">
                            <input type="hidden" name="incomeItemList[0][27].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][27].msiNo" value="28">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][27].kmm" maxlength="20" id="ssht_kmm_1_28" value="支出２５" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][27].kg" maxlength="14" id="ssht_kg_1_28" value="2,500" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][27].kmm" maxlength="20" id="icm_kmm_1_28" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][27].kg" maxlength="14" id="icm_kg_1_28" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][28].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][28].msiNo" value="29">
                            <input type="hidden" name="incomeItemList[0][28].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][28].msiNo" value="29">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][28].kmm" maxlength="20" id="ssht_kmm_1_29" value="支出２６" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][28].kg" maxlength="14" id="ssht_kg_1_29" value="2,600" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][28].kmm" maxlength="20" id="icm_kmm_1_29" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][28].kg" maxlength="14" id="icm_kg_1_29" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][29].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][29].msiNo" value="30">
                            <input type="hidden" name="incomeItemList[0][29].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][29].msiNo" value="30">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][29].kmm" maxlength="20" id="ssht_kmm_1_30" value="支出２７" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][29].kg" maxlength="14" id="ssht_kg_1_30" value="2,700" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][29].kmm" maxlength="20" id="icm_kmm_1_30" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][29].kg" maxlength="14" id="icm_kg_1_30" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][30].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][30].msiNo" value="31">
                            <input type="hidden" name="incomeItemList[0][30].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][30].msiNo" value="31">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][30].kmm" maxlength="20" id="ssht_kmm_1_31" value="自治会費" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][30].kg" maxlength="14" id="ssht_kg_1_31" value="1,000" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][30].kmm" maxlength="20" id="icm_kmm_1_31" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][30].kg" maxlength="14" id="icm_kg_1_31" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[0][31].bldCd" value="011278901">
                            <input type="hidden" name="expenditureItemList[0][31].msiNo" value="32">
                            <input type="hidden" name="incomeItemList[0][31].bldCd" value="011278901">
                            <input type="hidden" name="incomeItemList[0][31].msiNo" value="32">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[0][31].kmm" maxlength="20" id="ssht_kmm_1_32" value="水道管理手数料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[0][31].kg" maxlength="14" id="ssht_kg_1_32" value="500" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[0][31].kmm" maxlength="20" id="icm_kmm_1_32" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[0][31].kg" maxlength="14" id="icm_kg_1_32" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[1][0].bldCd" value="011278902">
                            <input type="hidden" name="expenditureItemList[1][0].msiNo" value="1">
                            <input type="hidden" name="incomeItemList[1][0].bldCd" value="011278902">
                            <input type="hidden" name="incomeItemList[1][0].msiNo" value="1">
                            
                                  <tr>
                                    <td class="output-rentroll-first-row" colspan="4">エム・○ル○エ○ (建物CD：011278902)</td>
                                  </tr>
                                
                            <tr>
                              <td><input type="text" name="expenditureItemList[1][0].kmm" maxlength="20" id="ssht_kmm_2_1" value="原状回復調整金" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[1][0].kg" maxlength="14" id="ssht_kg_2_1" value="2,838" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[1][0].kmm" maxlength="20" id="icm_kmm_2_1" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[1][0].kg" maxlength="14" id="icm_kg_2_1" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[1][1].bldCd" value="011278902">
                            <input type="hidden" name="expenditureItemList[1][1].msiNo" value="2">
                            <input type="hidden" name="incomeItemList[1][1].bldCd" value="011278902">
                            <input type="hidden" name="incomeItemList[1][1].msiNo" value="2">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[1][1].kmm" maxlength="20" id="ssht_kmm_2_2" value="オーナーズＧ保険料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[1][1].kg" maxlength="14" id="ssht_kg_2_2" value="834" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[1][1].kmm" maxlength="20" id="icm_kmm_2_2" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[1][1].kg" maxlength="14" id="icm_kg_2_2" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[1][2].bldCd" value="011278902">
                            <input type="hidden" name="expenditureItemList[1][2].msiNo" value="3">
                            <input type="hidden" name="incomeItemList[1][2].bldCd" value="011278902">
                            <input type="hidden" name="incomeItemList[1][2].msiNo" value="3">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[1][2].kmm" maxlength="20" id="ssht_kmm_2_3" value="ワランティサービス" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[1][2].kg" maxlength="14" id="ssht_kg_2_3" value="6,270" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[1][2].kmm" maxlength="20" id="icm_kmm_2_3" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[1][2].kg" maxlength="14" id="icm_kg_2_3" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[1][3].bldCd" value="011278902">
                            <input type="hidden" name="expenditureItemList[1][3].msiNo" value="4">
                            <input type="hidden" name="incomeItemList[1][3].bldCd" value="011278902">
                            <input type="hidden" name="incomeItemList[1][3].msiNo" value="4">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[1][3].kmm" maxlength="20" id="ssht_kmm_2_4" value="建物維持費" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[1][3].kg" maxlength="14" id="ssht_kg_2_4" value="200" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[1][3].kmm" maxlength="20" id="icm_kmm_2_4" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[1][3].kg" maxlength="14" id="icm_kg_2_4" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[1][4].bldCd" value="011278902">
                            <input type="hidden" name="expenditureItemList[1][4].msiNo" value="5">
                            <input type="hidden" name="incomeItemList[1][4].bldCd" value="011278902">
                            <input type="hidden" name="incomeItemList[1][4].msiNo" value="5">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[1][4].kmm" maxlength="20" id="ssht_kmm_2_5" value="自治会費" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[1][4].kg" maxlength="14" id="ssht_kg_2_5" value="2,000" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[1][4].kmm" maxlength="20" id="icm_kmm_2_5" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[1][4].kg" maxlength="14" id="icm_kg_2_5" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[1][5].bldCd" value="011278902">
                            <input type="hidden" name="expenditureItemList[1][5].msiNo" value="6">
                            <input type="hidden" name="incomeItemList[1][5].bldCd" value="011278902">
                            <input type="hidden" name="incomeItemList[1][5].msiNo" value="6">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[1][5].kmm" maxlength="20" id="ssht_kmm_2_6" value="水道管理手数料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[1][5].kg" maxlength="14" id="ssht_kg_2_6" value="600" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[1][5].kmm" maxlength="20" id="icm_kmm_2_6" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[1][5].kg" maxlength="14" id="icm_kg_2_6" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[1][6].bldCd" value="011278902">
                            <input type="hidden" name="expenditureItemList[1][6].msiNo" value="7">
                            <input type="hidden" name="incomeItemList[1][6].bldCd" value="011278902">
                            <input type="hidden" name="incomeItemList[1][6].msiNo" value="7">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[1][6].kmm" maxlength="20" id="ssht_kmm_2_7" value="支払済賃料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[1][6].kg" maxlength="14" id="ssht_kg_2_7" value="400" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[1][6].kmm" maxlength="20" id="icm_kmm_2_7" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[1][6].kg" maxlength="14" id="icm_kg_2_7" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[1][7].bldCd" value="011278902">
                            <input type="hidden" name="expenditureItemList[1][7].msiNo" value="8">
                            <input type="hidden" name="incomeItemList[1][7].bldCd" value="011278902">
                            <input type="hidden" name="incomeItemList[1][7].msiNo" value="8">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[1][7].kmm" maxlength="20" id="ssht_kmm_2_8" value="一般回収" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[1][7].kg" maxlength="14" id="ssht_kg_2_8" value="1,500" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[1][7].kmm" maxlength="20" id="icm_kmm_2_8" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[1][7].kg" maxlength="14" id="icm_kg_2_8" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[2][0].bldCd" value="011278903">
                            <input type="hidden" name="expenditureItemList[2][0].msiNo" value="1">
                            <input type="hidden" name="incomeItemList[2][0].bldCd" value="011278903">
                            <input type="hidden" name="incomeItemList[2][0].msiNo" value="1">
                            
                                  <tr>
                                    <td class="output-rentroll-first-row" colspan="4">エム・○ル○エ○ (建物CD：011278903)</td>
                                  </tr>
                                
                            <tr>
                              <td><input type="text" name="expenditureItemList[2][0].kmm" maxlength="20" id="ssht_kmm_3_1" value="原状回復調整金" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[2][0].kg" maxlength="14" id="ssht_kg_3_1" value="3,487" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[2][0].kmm" maxlength="20" id="icm_kmm_3_1" value="建物維持費" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[2][0].kg" maxlength="14" id="icm_kg_3_1" value="1,200" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[2][1].bldCd" value="011278903">
                            <input type="hidden" name="expenditureItemList[2][1].msiNo" value="2">
                            <input type="hidden" name="incomeItemList[2][1].bldCd" value="011278903">
                            <input type="hidden" name="incomeItemList[2][1].msiNo" value="2">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[2][1].kmm" maxlength="20" id="ssht_kmm_3_2" value="オーナーズＧ保険料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[2][1].kg" maxlength="14" id="ssht_kg_3_2" value="858" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[2][1].kmm" maxlength="20" id="icm_kmm_3_2" value="自治会費" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[2][1].kg" maxlength="14" id="icm_kg_3_2" value="100" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[2][2].bldCd" value="011278903">
                            <input type="hidden" name="expenditureItemList[2][2].msiNo" value="3">
                            <input type="hidden" name="incomeItemList[2][2].bldCd" value="011278903">
                            <input type="hidden" name="incomeItemList[2][2].msiNo" value="3">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[2][2].kmm" maxlength="20" id="ssht_kmm_3_3" value="ワランティサービス" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[2][2].kg" maxlength="14" id="ssht_kg_3_3" value="6,270" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[2][2].kmm" maxlength="20" id="icm_kmm_3_3" value="支払済賃料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[2][2].kg" maxlength="14" id="icm_kg_3_3" value="600" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[2][3].bldCd" value="011278903">
                            <input type="hidden" name="expenditureItemList[2][3].msiNo" value="4">
                            <input type="hidden" name="incomeItemList[2][3].bldCd" value="011278903">
                            <input type="hidden" name="incomeItemList[2][3].msiNo" value="4">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[2][3].kmm" maxlength="20" id="ssht_kmm_3_4" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[2][3].kg" maxlength="14" id="ssht_kg_3_4" value="0" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[2][3].kmm" maxlength="20" id="icm_kmm_3_4" value="一般回収" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[2][3].kg" maxlength="14" id="icm_kg_3_4" value="600" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[3][0].bldCd" value="011278904">
                            <input type="hidden" name="expenditureItemList[3][0].msiNo" value="1">
                            <input type="hidden" name="incomeItemList[3][0].bldCd" value="011278904">
                            <input type="hidden" name="incomeItemList[3][0].msiNo" value="1">
                            
                                  <tr>
                                    <td class="output-rentroll-first-row" colspan="4">エム・○ル○エ○ (建物CD：011278904)</td>
                                  </tr>
                                
                            <tr>
                              <td><input type="text" name="expenditureItemList[3][0].kmm" maxlength="20" id="ssht_kmm_4_1" value="原状回復調整金" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[3][0].kg" maxlength="14" id="ssht_kg_4_1" value="2,981" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[3][0].kmm" maxlength="20" id="icm_kmm_4_1" value="自治会費" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[3][0].kg" maxlength="14" id="icm_kg_4_1" value="450" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[3][1].bldCd" value="011278904">
                            <input type="hidden" name="expenditureItemList[3][1].msiNo" value="2">
                            <input type="hidden" name="incomeItemList[3][1].bldCd" value="011278904">
                            <input type="hidden" name="incomeItemList[3][1].msiNo" value="2">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[3][1].kmm" maxlength="20" id="ssht_kmm_4_2" value="オーナーズＧ保険料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[3][1].kg" maxlength="14" id="ssht_kg_4_2" value="834" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[3][1].kmm" maxlength="20" id="icm_kmm_4_2" value="支払済賃料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[3][1].kg" maxlength="14" id="icm_kg_4_2" value="420" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[3][2].bldCd" value="011278904">
                            <input type="hidden" name="expenditureItemList[3][2].msiNo" value="3">
                            <input type="hidden" name="incomeItemList[3][2].bldCd" value="011278904">
                            <input type="hidden" name="incomeItemList[3][2].msiNo" value="3">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[3][2].kmm" maxlength="20" id="ssht_kmm_4_3" value="ワランティサービス" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[3][2].kg" maxlength="14" id="ssht_kg_4_3" value="6,270" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[3][2].kmm" maxlength="20" id="icm_kmm_4_3" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[3][2].kg" maxlength="14" id="icm_kg_4_3" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[3][3].bldCd" value="011278904">
                            <input type="hidden" name="expenditureItemList[3][3].msiNo" value="4">
                            <input type="hidden" name="incomeItemList[3][3].bldCd" value="011278904">
                            <input type="hidden" name="incomeItemList[3][3].msiNo" value="4">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[3][3].kmm" maxlength="20" id="ssht_kmm_4_4" value="水道管理手数料" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[3][3].kg" maxlength="14" id="ssht_kg_4_4" value="600" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[3][3].kmm" maxlength="20" id="icm_kmm_4_4" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[3][3].kg" maxlength="14" id="icm_kg_4_4" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                            <input type="hidden" name="expenditureItemList[3][4].bldCd" value="011278904">
                            <input type="hidden" name="expenditureItemList[3][4].msiNo" value="5">
                            <input type="hidden" name="incomeItemList[3][4].bldCd" value="011278904">
                            <input type="hidden" name="incomeItemList[3][4].msiNo" value="5">
                            
                            <tr>
                              <td><input type="text" name="expenditureItemList[3][4].kmm" maxlength="20" id="ssht_kmm_4_5" value="一般回収" class="output-rentroll">
                              </td>
                              <td><input type="text" name="expenditureItemList[3][4].kg" maxlength="14" id="ssht_kg_4_5" value="400" class="output-rentroll-100">
                              </td>
                              <td><input type="text" name="incomeItemList[3][4].kmm" maxlength="20" id="icm_kmm_4_5" value="" class="output-rentroll">
                              </td>
                              <td><input type="text" name="incomeItemList[3][4].kg" maxlength="14" id="icm_kg_4_5" value="0" class="output-rentroll-100">
                              </td>
                            </tr>
                          
                        <tr>
                          <td class="thead-rentroll-173">月額合計</td>
                          <td><input type="text" name="totalItem.sshkGBfprMggkRbct" maxlength="14" value="82,522" class="output-rentroll-100" readonly=""></td>
                          <td class="thead-rentroll-173">月額合計</td>
                          <td><input type="text" name="totalItem.icmBfprMggkRbct" maxlength="14" value="4,070" class="output-rentroll-100" readonly=""></td>
                        </tr>
                        <tr>
                          <td>年額合計</td>
                          <td><input type="text" name="totalItem.sshkGBfprYgkGkRbct" maxlength="14" value="990,264" class="output-rentroll-100" readonly=""></td>
                          <td>年額合計</td>
                          <td><input type="text" name="totalItem.icmBfprYgkGkRbct" maxlength="14" value="48,840" class="output-rentroll-100" readonly=""></td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                  <td align="left" valign="top">
                    <table border="1" align="center">
                      <tbody>
                        <tr>
                          <td colspan="2">収入（合計）</td>
                        </tr>
                        <tr>
                          <td>月額収入</td>
                          <td><input type="text" name="totalItem.icmGkBfprMgIcmRbct" maxlength="14" value="454,386" class="output-rentroll-100" readonly=""></td>
                        </tr>
                        <tr>
                          <td>年間収入</td>
                          <td><input type="text" name="totalItem.icmGkBfprYknIcmRbct" maxlength="14" value="5,452,632" class="output-rentroll-100" readonly=""></td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
    </tbody></table>
    <!-- 画面表示で使用しないが建物CDのみ保持する必要あり -->
        <input type="hidden" name="totalItemList[0].bldCd" value="011278901">
    
        <input type="hidden" name="totalItemList[1].bldCd" value="011278902">
    
        <input type="hidden" name="totalItemList[2].bldCd" value="011278903">
    
        <input type="hidden" name="totalItemList[3].bldCd" value="011278904">
    <div>
<input type="hidden" name="_csrf" value="fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1">
</div></form>
  </div>
</div>

</body></html>