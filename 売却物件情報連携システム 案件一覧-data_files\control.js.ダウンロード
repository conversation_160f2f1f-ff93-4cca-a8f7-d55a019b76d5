//ブラウザバック制御
history.pushState(null, null, null);

window.addEventListener("popstate", function() {
    history.pushState(null, null, null);
});

var kentaku;

$(function() {
    // 二重送信制御
    $('.decide_button, #result').click(function() {
        $(this).click(function() {
            return false;
        });
    });

});

(function() {
    kentaku = {
        code : {
            /**
             * <pre>
             * ※部品内部のprivate関数
             * 項目のid属性の名前解決を行います。
             * @param {String} itemId 項目のid属性
             * @param {String} rowIndex 項目の行番号
             * </pre>
             */
            _resolveItemId : function(itemId, rowIndex) {
                var targetItemId = itemId;
                if (targetItemId.indexOf('.') != -1 && rowIndex != null) {
                    // 明細向けのID変換
                    var targetIds = targetItemId.split('.');
                    targetItemId = targetIds[0] + rowIndex + '.' + targetIds[1];
                }
                return targetItemId;
            },
            /**
             * <pre>
             * ※部品内部のprivate関数
             * ダイアログの設定に不足している項目を追加します。
             * 追加する情報はアクションコンテキストと[フォームID].jsのコード定義から取得します。
             * </pre>
             * 
             * @param {Object}
             *            context アクションコンテキスト
             * @param {Object}
             *            param ダイアログの設定
             */
            _assistParam : function(context, param) {
                if (!param) {
                    param = {};
                }
                // コードIDの取得
                if (!param.codeId) {
                    param.codeId = codeAssociation[context['actionId']].codeId;
                    if (!param.codeId) {
                        alert('コードIDは必ず指定してください。');
                        return;
                    }
                }
                // 検索結果を設定する項目マップの取得
                if (!param.resultMapping) {
                    param.resultMapping = codeAssociation[context['actionId']].mapping;
                }
                // 対象の行番号の取得
                if (!param.rowIndex) {
                    param.rowIndex = context['rowIndex'];
                }
                // コントローラへ渡す制御用のパラメータが存在しなければ空オブジェクトで初期化する
                if (!param.parameter) {
                    param.parameter = {};
                }
            },
            /**
             * パラメータで指定された項目の値に対してコード検索結果を設定します。
             * 
             * @param {Object}
             *            param ダイアログの設定
             * @param {Object}
             *            results コード検索結果リスト
             * @param {Object}
             *            context アクションコンテキスト
             */
            setCodeData : function(param, results, context) {
                // もし個別のデータセットメソッドが宣言されているなら、そちらに実施してもらう
                var actionId = context.actionId;
                var rowIndex = param.rowIndex;
                var actionMethodName = 'setData' + actionId[0].toUpperCase() + actionId.slice(1);
                // if (formInfo.actions[actionMethodName]) {
                // // データセットメソッドが定義されていれば呼び出す
                // var setItems = formInfo.actions[actionMethodName](actionId,
                // rowIndex, results);
                //
                // if (!setItems) {
                // for ( var itemId in setItems) {
                //
                // // 値を設定する対象項目のオブジェクトを取得
                // var targetItem = $('[id="' +
                // kentaku.code._resolveItemId(itemId, param.rowIndex) + '"]');
                // if (targetItem.length <= 0) {
                // // 対象項目が画面上に存在しない場合は次の列へ進む
                // continue;
                // }
                // // // 全角半角コンバート処理
                // // if (convert[itemId]) {
                // // kentaku.convert.convertString(targetItem,
                // // convert[itemId]);
                // // }
                // // // フォーマット処理
                // // var formatParams = format[itemId];
                // // if (formatParams) {
                // //
                // targetItem.val(formatters[formatParams.formatter].print(targetItem.val(),
                // // formatParams.maxLength, formatParams.pattern));
                // // }
                //
                // // 業務の実装向けにイベントを発行する
                // targetItem.trigger('afterCodeSetting', [ param.rowIndex ]);
                // }
                // }
                // return;
                // }

                // 検索結果の列数分ループする。
                for ( var dataId in results) {
                    if (results[dataId] == null) {
                        // 値が存在しない場合は次の列へ進む
                        continue;
                    }

                    // 親画面のコード定義から値を設定する対象項目情報を取得
                    var targetItemInfo = param.resultMapping[dataId];
                    if (!targetItemInfo) {
                        // 項目情報が存在しない場合は次の列へ進む
                        continue;
                    }

                    // 値を設定する対象項目のオブジェクトを取得
                    var targetItem = $('[id="' + kentaku.code._resolveItemId(targetItemInfo.itemId, param.rowIndex) + '"]');
                    if (targetItem.length <= 0) {
                        // 対象項目が画面上に存在しない場合は次の列へ進む
                        continue;
                    }
                    // 対象項目に検索結果を設定
                    targetItem.val(results[dataId]);

                    // // 全角半角コンバート処理
                    // if (convert[targetItemInfo.itemId]) {
                    // kentaku.convert.convertString(targetItem,
                    // convert[targetItemInfo.itemId]);
                    // }
                    // // フォーマット処理
                    // var formatParams = format[targetItemInfo.itemId];
                    // if (formatParams) {
                    // targetItem.val(formatters[formatParams.formatter].print(targetItem.val(),
                    // formatParams.maxLength, formatParams.pattern));
                    // }

                    // 業務の実装向けにイベントを発行する
                    targetItem.trigger('afterCodeSetting', [ param.rowIndex ]);
                }
            },
            /**
             * <pre>
             * コードのキー項目に対するブラーイベント発生時処理を行います。
             * コード名称取得の対象は[フォームID].jsのコード定義で設定します。
             * </pre>
             * 
             */
            executeOnBlur : function() {
                if (typeof (codeAssociation) === 'undefined' || !codeAssociation) {
                    // [フォームID].jsにコード定義が存在しない場合は処理を終了
                    return;
                }

                var itemName = event.target.name;
                // ブラーイベントが発生した項目のname属性から、コード定義に設定される項目名を取得
                var targetName = itemName.match(/.+\[([0-9]+)\].+/) ? itemName.replace(/\[[0-9]+\]/, '') : itemName; // 明細に対応
                var rowIndex = itemName.match(/.+\[([0-9]+)\].+/) ? itemName.replace(/.+\[([0-9]+)\].+/, '$1') : null; // 明細に対応

                var targetCodeAction = "";
                // コード定義から対象となるコードIDを取得
                for ( var codeAction in codeAssociation) {
                    // [フォームID].jsにコードアクション用の関数が存在する場合は実行する
                    var context = {
                        'actionId' : codeAction,
                        'rowIndex' : rowIndex,
                        'codeType' : 'keySearch'
                    };

                    // コードアクション用の関数から項目定義を取得する
                    var param = {};
                    var customSearchCondition = {};

                    var actionMethodName = 'invoke' + codeAction[0].toUpperCase() + codeAction.slice(1);
                    // if (formInfo.actions[actionMethodName] &&
                    // !formInfo.actions[actionMethodName](context, param,
                    // customSearchCondition)) {
                    // continue;
                    // }
                    // // コンテキストからダイアログの設定へ転記
                    kentaku.code._assistParam(context, param);

                    // マッピングされた項目が存在するかチェック
                    for ( var codeItemId in param.resultMapping) {
                        if (param.resultMapping[codeItemId].itemId === targetName) {
                            if (param.resultMapping[codeItemId].key) {
                                targetCodeAction = codeAction;
                                break;
                            }
                        }
                    }
                    if (targetCodeAction) {
                        break;
                    }
                }
                if (!targetCodeAction) {
                    // コードIDが取得できない場合は処理を終了
                    return;
                }

                // サーバへ渡す検索条件オブジェクト
                if (0 < Object.keys(customSearchCondition).length) {
                    // 実装者によるカスタム検索条件が指定されている場合、そちらを使う
                    context.searchCondition = customSearchCondition;
                } else {
                    // 指定が無ければ画面JSの定義から
                    var searchCondition = {};
                    var codeMapping = param.resultMapping;
                    var isEmptyKeyValueExists = false; // 空のキー項目存在フラグ
                    // 定義情報のキー項目を基に、検索条件を取得する
                    for ( var codeItemId in codeMapping) {
                        var targetItemId = kentaku.code._resolveItemId(codeMapping[codeItemId].itemId, rowIndex);
                        if (!codeMapping[codeItemId].key) {
                            // 名称項目の場合は、値を消しておく
                            $('[id="' + targetItemId + '"]').val('');
                        } else {
                            searchCondition[codeItemId] = $('[id="' + targetItemId + '"]').val();
                            if ($('[id="' + targetItemId + '"]').val().length <= 0) {
                                // 名称項目の値を空にするためループは最後まで回す。
                                isEmptyKeyValueExists = true;
                            }
                        }
                    }
                    // キー項目の値が一つでも空なら処理を終了。
                    if (isEmptyKeyValueExists) {
                        return;
                    }
                    context.searchCondition = searchCondition;
                }

                $.extend(param.parameter, context.searchCondition);
                var parameterArray = [];
                // 名称取得リクエストに付与するパラメータ
                for ( var key in param.parameter) {
                    parameterArray.push(key + '=' + encodeURIComponent(param.parameter[key]));
                }

                $.ajax({
                    type : "GET",
                    url : contextRoot + '/code/' + param.codeId + '/keySearch?' + parameterArray.join('&'),
                }).done(function(json) {
                    kentaku.code.setCodeData(param, json, context);
                }).fail(function(xhr) {
                    console.log(xhr);
                });
            }
        }
    };
}());

// 項目の値を保存するための変数
var focusItemId;
var focusItemValue;

/**
 * 画面項目のフォーカス時イベント
 */
$(document).on('focus', 'input,textarea,select', function() {
    // フォーカス時にフォーカスされた項目の値をとっておく
    focusItemId = event.target.id;
    focusItemValue = $(event.target).val();

    // DKWORKSと違うのでフォーマット処理は不要？
    // if (focusItemId) {
    // // フォーカスされた項目がinputもしくはtextareaの場合
    // if ($(this).is('input, textarea')) {
    // // フォーカスされた項目がreadonly属性でない場合
    // if ($(this).is('[readonly]') == false) {
    // // フォーマット処理(全角/半角変換後に実行すること)
    // formatters.executeOnFocus();
    // }
    // }
    // }
});
/**
 * 画面項目のブラー時イベント
 */
$(document).on('blur', 'input,textarea,select', function() {

    // // 全角/半角変換処理
    // kentaku.convert.executeOnBlur();

    // // フォーカスされた項目がinputもしくはtextareaの場合
    // if ($(this).is('input, textarea')) {
    // // フォーカスされた項目がreadonly属性でない場合
    // if ($(this).is('[readonly]') == false) {
    // // フォーマット処理(全角/半角変換後に実行すること)
    // formatters.executeOnBlur();
    // }
    // }

    // フォーカス時とロストフォーカス時の値を比較し変わっていればdkChangeイベントを発行する（コード名称取得用）
    if (focusItemId === event.target.id && focusItemValue != $(event.target).val()) {
        $(event.target).trigger('dkChanged');
    }
    // 値を削除する
    focusItemId = "";
    focusItemValue = "";
});
/**
 * 値が変更された画面項目のブラー時イベント
 */
$(document).on('dkChanged', function() {
    // コード名称取得処理(全角/半角変換,フォーマット後に実行すること)
    kentaku.code.executeOnBlur();
});