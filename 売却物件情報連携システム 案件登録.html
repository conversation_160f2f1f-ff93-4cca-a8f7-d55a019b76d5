<!DOCTYPE html PUBLIC "-//W3C//Dtd HTML 4.01 transitional//EN" "http://www.w3.org/tr/html4/loose.dtd">
<!-- saved from url=(0101)http://devap9001/BaibaiJohoKanri/add/btnView?jchCd=0267362&_csrf=fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1 -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  
  <title>売却物件情報連携システム 案件登録</title>

  
  <link href="./売却物件情報連携システム 案件登録_files/common.css" rel="stylesheet">

  
  <script src="./売却物件情報連携システム 案件登録_files/jquery-2.2.0.min.js.ダウンロード"></script>
  
  <script src="./売却物件情報連携システム 案件登録_files/control.js.ダウンロード"></script>

<script type="text/javascript">
<!--

$(function() {
  // 表示を戻すボタン押下時の確認メッセージ
  $('#button_back').click(function(e) {

    if(!confirm('表示を戻してよろしいですか？')){
      /* キャンセルの時の処理 */
      return false;
    }
    return true;
  });

  // 登録ボタン押下時の必須チェック
  $('#button_reg').click(function(e) {

    $('.error_msg').empty();

    var noChecked = true;
    $('input[type=checkbox]').each(function() {
        if( $(this).prop("checked") == true ) {
            noChecked = false;
            $(this).val("1");
        }
    });

    if(noChecked) {
        alert('売買対象の建物がチェックされていません');
        return false;
    }

    if($('#egsCd').val() == "") {
        alert('管理営業所コードが入力されていません');
        $('#egsCd').focus();
        return false;
    }
    if($('#egsCd').val().length != 3) {
        alert('管理営業所コードは３桁で入力してください');
        $('#egsCd').focus();
        return false;
    }
    if (!$('#egsCd').val().match(/^[0-9]*$/)) {
        alert('管理営業所コードは半角数値で入力してください');
        $('#egsCd').focus();
        return false;
    }

    if($('#ptnsTtm').val() == "") {
        alert('パートナーズ担当者が入力されていません');
        $('#egsCd').focus();
        return false;
    }
    if($('#ptnsTtm').val().length != 6) {
        alert('パートナーズ担当者は６桁で入力してください');
        $('#egsCd').focus();
        return false;
    }
    if (!$('#ptnsTtm').val().match(/^[0-9]*$/)) {
        alert('パートナーズ担当者は半角数値で入力してください');
        $('#egsCd').focus();
        return false;
    }

    if($('#stoCd').val() == "") {
        alert('店舗コードが入力されていません');
        $('#stoCd').focus();
        return false;
    }
    if($('#stoCd').val().length != 3) {
        alert('店舗コードは３桁で入力してください');
        $('#stoCd').focus();
        return false;
    }
    if (!$('#stoCd').val().match(/^[0-9]*$/)) {
        alert('店舗コードは半角数値で入力してください');
        $('#stoCd').focus();
        return false;
    }

    if($('#stoTtm').val() == "") {
        alert('リーシング担当者が入力されていません');
        $('#stoTtm').focus();
        return false;
    }
    if($('#stoTtm').val().length != 6) {
        alert('リーシング担当者は６桁で入力してください');
        $('#stoTtm').focus();
        return false;
    }
    if (!$('#stoTtm').val().match(/^[0-9]*$/)) {
        alert('リーシング担当者は半角数値で入力してください');
        $('#stoTtm').focus();
        return false;
    }

    // 登録ボタン押下時の確認メッセージ
    if(!confirm('登録してよろしいですか？')){
        return false;
    }
    return true;
  });
  
  contextRoot = '/BaibaiJohoKanri';

  // ----------------------コード定義----------------------------------------------------
  codeAssociation = {
          // パートナーズ営業所コード
          'egsNm' : {
              'codeId' : 'office',
              'mapping' : {
                  'jgsKb' : {
                      itemId : 'egsJgsKb',
                      key : true
                  },
                  'jscd6Kt' : {
                      itemId : 'egsCd',
                      key : true
                  },
                  'jgsNm' : {
                      itemId : 'egsNm'
                  }
              }
          },
          // パートナーズ担当者コード
          'ptnsTtmNm' : {
              'codeId' : 'employee',
              'mapping' : {
                  'empNo' : {
                      itemId : 'ptnsTtm',
                      key : true
                  },
                  'empNm' : {
                      itemId : 'ptnsTtmNm'
                  }
              }
          },
          // リーシング店舗コード
          'stoNm' : {
              'codeId' : 'office',
              'mapping' : {
                  'jgsKb' : {
                      itemId : 'stoJgsKb',
                      key : true
                  },
                  'jscd6Kt' : {
                      itemId : 'stoCd',
                      key : true
                  },
                  'jgsNm' : {
                      itemId : 'stoNm'
                  }
              }
          },
          // リーシング担当者コード
          'stoTtmNm' : {
              'codeId' : 'employee',
              'mapping' : {
                  'empNo' : {
                      itemId : 'stoTtm',
                      key : true
                  },
                  'empNm' : {
                      itemId : 'stoTtmNm'
                  }
              }
          }
  }
});

$(window).load(function(){
    $('input[type=checkbox]').each(function() {
        if( $(this).val() == "1" ) {
            $(this).prop("checked", true);
        }
    });
  });
//-->
</script>
</head>
<body>
  <div style="text-align: center;">
    
    <h2 style="display: inline-block;">売却物件情報連携システム</h2>
  </div>

  <div class="container">
  <div class="contents">
    <p> 【以下の情報を入力して、売却物件を登録してください】</p>
    <div class="inputClass">
      <form id="button" action="http://devap9001/BaibaiJohoKanri/add" method="GET">
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th><th class="width10"></th>

          </tr>
          <tr>
            <td colspan="3">建物コード（先頭７桁）</td>
            <td colspan="8">
              <input type="text" name="jchCd" class="input-code-text" maxlength="7" value="0267362" readonly="" disabled="">
            </td>
          </tr>
          <tr>
            <td colspan="9"></td>
            <td align="right">
              <input type="submit" class="add-button" value="表示" disabled="">
            </td>
          </tr>
          <tr>
            <td colspan="9"></td>
            <td align="right">
              <button type="submit" id="button_back" class="add-button">表示を戻す</button>
              <input type="hidden" name="_csrf" value="fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1">
            </td>
          </tr>
        </tbody>
      </table>
      <div>
</div></form>
    </div>

    <hr>

    <div class="outputClass">
      <form id="button" action="http://devap9001/BaibaiJohoKanri/add/btnReg" method="POST">
      <table border="0">
        <tbody>
          <tr>
            <!-- レイアウト調整用１０分割 -->
            <th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th><th class="width10"></th><th class="width10"></th>
            <th class="width10"></th>
          </tr>
          
          <tr>
            <td colspan="2">
            <input id="regCheck0" name="ankenAddDataList[0].regCheck" type="checkbox" value="">
            <label for="regCheck0">売買対象</label>
            </td>
            <td>　</td>
            <td>建物コード：</td>
            <td>
            <input type="text" name="ankenAddDataList[0].bldCd" class="output-code-text" value="026736201" readonly="">
            </td>
            <td>　</td>
            <td>建物名：</td>
            <td>
              <input type="text" name="ankenAddDataList[0].bldNm" class="output-code-text" value="アンジ○ー○Ⅲ" readonly="">
            </td>
          </tr>
          <tr>
            <td colspan="7">　</td>
          </tr>
          
          <tr>
            <td colspan="3">パートナーズ管理営業所：</td>
            <td colspan="2">
              <input type="text" name="egsCd" id="egsCd" class="input-code-text" maxlength="3" value="">
            </td>
            <td colspan="5">
              <input type="text" name="egsNm" id="egsNm" class="output-text" value="" readonly="">
            </td>
          </tr>
          <tr>
            <td colspan="3">パートナーズ担当者：</td>
            <td colspan="2">
              <input type="text" name="ptnsTtm" id="ptnsTtm" class="input-code-text" maxlength="6" value="">
            </td>
            <td colspan="5">
              <input type="text" name="ptnsTtmNm" id="ptnsTtmNm" class="output-text" maxlength="3" value="" readonly="">
            </td>
          </tr>
          <tr>
            <td colspan="3">リーシング店舗：</td>
            <td colspan="2">
              <input type="text" name="stoCd" id="stoCd" class="input-code-text" maxlength="3" value="">
            </td>
            <td colspan="5">
              <input type="text" name="stoNm" id="stoNm" class="output-text" value="" readonly="">
            </td>
          </tr>
          <tr>
            <td colspan="3">リーシング担当者：</td>
            <td colspan="2">
              <input type="text" name="stoTtm" id="stoTtm" class="input-code-text" maxlength="6" value="">
            </td>
            <td colspan="5">
              <input type="text" name="stoTtmNm" id="stoTtmNm" class="output-text" value="" readonly="">
            </td>
          </tr>
          <tr>
            <td colspan="9"></td>
            <td align="right">
              <button type="submit" id="button_reg" class="add-button">登録</button>
              <input type="hidden" name="_csrf" value="fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1">
              <input type="hidden" name="jchCd" value="0267362">
            </td>
          </tr>
        </tbody>
      </table>
      <input type="hidden" name="egsJgsKb" id="egsJgsKb" value="F">
      <input type="hidden" name="stoJgsKb" id="stoJgsKb" value="E">
      <div>
<input type="hidden" name="_csrf" value="fc4eb8c0-f77e-4c7b-a1de-98b40aafddc1">
</div></form>
    </div>
    <div class="error_msg">
    
    </div>
  </div>
  </div>

</body></html>