<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>自社物件売却受付時チェックシート</title>
    <style>
        body {
            font-family: "MS Gothic", monospace;
            font-size: 12px;
            margin: 10px;
            background-color: #f0f8ff;
        }
        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border: 2px solid #000;
        }
        .header {
            background-color: #e6f3ff;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
            padding: 10px;
            border-bottom: 2px solid #000;
        }
        .section {
            border-bottom: 1px solid #000;
            padding: 5px;
        }
        .section-title {
            background-color: #d0e7ff;
            font-weight: bold;
            padding: 5px;
            border-bottom: 1px solid #000;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }
        th, td {
            border: 1px solid #000;
            padding: 3px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #e6f3ff;
            font-weight: bold;
            text-align: center;
        }
        .checkbox-cell {
            text-align: center;
            width: 30px;
        }
        .amount-cell {
            text-align: right;
            width: 80px;
        }
        .date-cell {
            width: 100px;
        }
        .narrow {
            width: 60px;
        }
        .wide {
            width: 200px;
        }
        .note-section {
            background-color: #f9f9f9;
            padding: 10px;
            margin: 5px 0;
        }
        .button-section {
            text-align: center;
            padding: 15px;
            background-color: #f0f8ff;
        }
        .btn {
            padding: 8px 20px;
            margin: 0 10px;
            font-size: 12px;
            border: 1px solid #000;
            background-color: #e6f3ff;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #d0e7ff;
        }
        input[type="text"], input[type="date"], select {
            border: 1px solid #ccc;
            padding: 2px;
            font-size: 11px;
        }
        input[type="text"]:disabled {
            background-color: #f5f5f5;
            color: #666;
            border: 1px solid #ddd;
        }
        .small-input {
            width: 60px;
        }
        .medium-input {
            width: 100px;
        }
        .large-input {
            width: 150px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            自社物件売却受付時チェックシート
        </div>

        <div class="note-section">
            <strong>【 リーシング ・ パートナーズ　それぞれの対応について 】</strong>　※売却物件情報連携システム（以下、システム）対応についても記載しています。<br><br>

            <strong>　≪リーシング対応フロー≫</strong><br>
            　　① 自社物件受付時チェックシートの下記≪リーシング入力欄≫へ物件情報を入力のうえ、保存を行う<br>
            　　② 保存後、システムの「パートナーズへ依頼」を押下してパートナーズへ対応依頼を行う<br><br>

            <strong>　≪パートナーズ対応フロー≫</strong><br>
            　　① リーシングからの対応依頼後、チェックシートを確認し、下記≪パートナーズ使用欄≫へ情報を入力のうえ、担当者・管理職の名前が表示される<br>
            　　② チェックシートを記入後、下記添付資料の添付を行う<br>
            　　　　 &lt;システム添付資料&gt;<br>
            　　　　 　１．合意解約時賃貸人契約内容一覧表、２．追加営繕工事履歴一覧表、３．合意解約ご確認書、４．新特約解約金算出ツール、<br>
            　　　 　　５．一括賃貸借契約書または管理委託契約書、６．賃貸収入ご報告書、７．クレームWEBシステムの画面印刷<br>
            　　③ 添付後、システムのガス会社（ガスパル o rそれ以外）を選択し、「リーシングへ回答」を押下して回答を行う<br>
            　　　 　※上記③完了後は、オーナー様へ合意解約について訪問説明を実施し、システムへ署名捺印済みの合意解約ご確認書を添付のうえ、報告を行ってください。
        </div>

        <div class="section-title">◆リーシング使用欄◆</div>
        <table>
            <tr>
                <td style="border: none; padding: 5px; width: 33.33%;">
                    建物名称　<input type="text" style="width: 120px;" disabled>
                </td>
                <td style="border: none; padding: 5px; width: 33.33%;">
                    契約形態　<input type="text" style="width: 120px;" disabled>
                </td>
                <td style="border: none; padding: 5px; width: 33.33%;">
                    現賃貸人名　<input type="text" style="width: 120px;" disabled>
                </td>
            </tr>
            <tr>
                <td style="border: none; padding: 5px; width: 33.33%;">
                    建物CD　<input type="text" style="width: 120px;" disabled>
                </td>
                <td style="border: none; padding: 5px; width: 33.33%;">
                    建物完成日　<input type="text" style="width: 120px;" disabled>
                </td>
                <td style="border: none; padding: 5px; width: 33.33%;">
                    建物種別　<input type="text" style="width: 120px;" disabled>
                </td>
            </tr>
        </table>

        <div class="section-title">◆パートナーズ使用欄◆</div>
        <div style="font-weight: bold; margin: 5px 0;">1. 売却される契約・特約等</div>
        <table>
            <tr>
                <th class="wide">特約名</th>
                <th class="narrow">建物名称</th>
                <th class="wide">対象範囲（本体入居部分）</th>
                <th class="amount-cell">違約金・解約金（税込）</th>
            </tr>
            <tr>
                <td>①回線使用料等業務委託（2年特約）</td>
                <td><input type="text" class="small-input"></td>
                <td></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>②有料放送設備業務委託（2年特約）</td>
                <td><input type="text" class="small-input"></td>
                <td></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>③特別清掃業務委託</td>
                <td><input type="text" class="small-input"></td>
                <td></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>④特別清掃業務委託特約対象入居者</td>
                <td><input type="text" class="small-input"></td>
                <td></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>⑤回線使用料等業務委託（2年特約）</td>
                <td><input type="text" class="small-input"></td>
                <td></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>⑥専用回線業務委託特約</td>
                <td><input type="text" class="small-input"></td>
                <td></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>⑦その他売却業務委託（設備のため）</td>
                <td><input type="text" class="small-input"></td>
                <td></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>⑧特別清掃業務委託特約</td>
                <td><input type="text" class="small-input"></td>
                <td></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>⑨特別フォーム料金</td>
                <td><input type="text" class="small-input"></td>
                <td></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td colspan="3" style="text-align: center; font-weight: bold;">①～⑨の違約金合計</td>
                <td class="amount-cell">¥0 円</td>
            </tr>
        </table>

        <div style="font-weight: bold; margin: 15px 0 5px 0;">2. 建物工事等　実施の有無（工事業者の変更等で確認してください。なお契約時の建物工事の変更等も含む）</div>
        <table>
            <tr>
                <th class="wide">項目</th>
                <th class="narrow">建物名称</th>
                <th class="wide">工事内容</th>
            </tr>
            <tr>
                <td>①工事</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
            <tr>
                <td>②修繕</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
            <tr>
                <td>③設備改良</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
            <tr>
                <td>④その他</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
        </table>

        <div style="font-weight: bold; margin: 15px 0 5px 0;">3. ガス会社　建物毎に契約している場合等、変更</div>
        <table>
            <tr>
                <th class="wide">ガス会社名</th>
                <th class="narrow">建物名称</th>
                <th class="wide">担当者</th>
            </tr>
            <tr>
                <td><input type="text" class="large-input"></td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
        </table>

        <div style="font-weight: bold; margin: 15px 0 5px 0;">4. その他の項目</div>
        <table>
            <tr>
                <th class="wide">加入保険/業者</th>
                <th class="narrow">建物名称</th>
                <th class="wide">加入プラン・金額等</th>
            </tr>
            <tr>
                <td>①メンテナンスサービス</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
            <tr>
                <td>②清掃業者</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
            <tr>
                <td>③ライフルーム（各設備管理業務委託）</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
            <tr>
                <td>④その他</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
            <tr>
                <td>⑤設備管理</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
            <tr>
                <td>⑥保守点検（エレベーター等）</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
            <tr>
                <td>⑦保守点検（消防設備等）■点検ニーズ<br>■JCOM　■ユーネット</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
        </table>

        <div style="font-weight: bold; margin: 15px 0 5px 0;">5. 建売パック可能</div>
        <table>
            <tr>
                <th class="wide">項目名</th>
                <th class="narrow">建物名称</th>
                <th class="wide">備考</th>
                <th class="amount-cell">違約金・解約金（税込）</th>
            </tr>
            <tr>
                <td>①リフォームローン</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>②ケーブル</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>③保険/上り業務委託特約</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td>④その他</td>
                <td><input type="text" class="small-input"></td>
                <td><input type="text" class="large-input"></td>
                <td class="amount-cell">円</td>
            </tr>
            <tr>
                <td colspan="3" style="text-align: center; font-weight: bold;">①～④の違約金合計</td>
                <td class="amount-cell">¥0 円</td>
            </tr>
        </table>

        <div style="font-weight: bold; margin: 15px 0 5px 0;">6. 入居者情報確認（民泊・転貸）　売却上問題となる場合（転貸の場合、転貸上問題となる場合、民泊業者の場合等）</div>
        <table>
            <tr>
                <th class="wide">対象物件の確認事項</th>
                <th class="wide">民泊届出期間（●年●月）</th>
                <th class="wide">備考</th>
            </tr>
            <tr>
                <td style="height: 40px;"><input type="text" class="large-input"></td>
                <td><input type="text" class="large-input"></td>
                <td><input type="text" class="large-input"></td>
            </tr>
        </table>

        <div class="button-section">
            <table style="border: none; margin: 0 auto;">
                <tr>
                    <td style="border: none; padding: 10px;">
                        担当者（パートナーズ）<input type="text" class="medium-input" style="margin-left: 10px;">
                    </td>
                    <td style="border: none; padding: 10px;">
                        管理者（パートナーズ）<input type="text" class="medium-input" style="margin-left: 10px;">
                    </td>
                    <td style="border: none; padding: 10px;">
                        <button class="btn">保存</button>
                    </td>
                </tr>
                <tr>
                    <td colspan="3" style="border: none; text-align: center; padding: 10px;">
                        <button class="btn">クリア</button>
                        <button class="btn">条件詳細へ戻る</button>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <script>
        // 金額計算機能
        function calculateTotal() {
            // 違約金合計の計算ロジックをここに実装
        }

        // フォームのクリア機能
        function clearForm() {
            if(confirm('入力内容をクリアしてよろしいですか？')) {
                document.querySelectorAll('input[type="text"], input[type="date"]').forEach(input => {
                    input.value = '';
                });
            }
        }

        // 保存機能
        function saveForm() {
            if(confirm('入力内容を保存してよろしいですか？')) {
                // 保存処理をここに実装
                alert('保存しました。');
            }
        }

        // ボタンイベントの設定
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                if(button.textContent === 'クリア') {
                    button.onclick = clearForm;
                } else if(button.textContent === '保存') {
                    button.onclick = saveForm;
                } else if(button.textContent === '条件詳細へ戻る') {
                    button.onclick = function() {
                        if(confirm('条件詳細画面に戻りますか？')) {
                            // 戻る処理をここに実装
                            history.back();
                        }
                    };
                }
            });
        });
    </script>
</body>
</html>