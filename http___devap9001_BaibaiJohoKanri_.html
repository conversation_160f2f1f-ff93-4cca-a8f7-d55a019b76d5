﻿

<!DOCTYPE html PUBLIC "-//W3C//Dtd HTML 4.01 transitional//EN" "http://www.w3.org/tr/html4/loose.dtd">
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>売却物件情報連携システム ログイン</title>

  
  <link href="/BaibaiJohoKanri/css/common.css" rel="stylesheet" />

  
  <script src="/BaibaiJohoKanri/js/jquery-2.2.0.min.js"></script>
  
  <script src="/BaibaiJohoKanri/js/control.js"></script>

<script type="text/javascript">
<!--

$(function() {
  // ログインフォーム必須入力チェック
  $('#button_login').click(function(e) {

    $('.error_msg').empty();

    // username必須チェック
    if($('#username').val() == "") {
        $('.error_msg').append('ユーザＩＤが入力されていません');
        $('#username').focus();
        return false;
    }

    // username入力値チェック
    if (!$('#username').val().match(/^[0-9]*$/)) {
        $('.error_msg').append('ユーザＩＤは半角数値で入力してください');
        $('#username').focus();
        return false;
    }

    // password必須チェック
    if($('#071350').val() == ""){
        $('.error_msg').append('パスワードが入力されていません');
        $('#password').focus();
        return false;
    }

    return true;
  });
});
//-->
</script>
</head>
<body>
  <form id="command" action="/BaibaiJohoKanri/authentication" method="post">
  <h2 align="center">売却物件情報連携システム</h2>
  <table frame="border" align="center" border="0" cellspacing="0" cellpadding="20" width="485" bordercolor="black">
    <tr>
      <td>
        <table width="325" border="1" align="center">
          <tbody>
            <tr>
              <td class="hpb-cnt-tb-cell1" width="130" style="padding-left:15px;" align="left">ユーザＩＤ</td>
              <td class="hpb-cnt-tb-cell2" height="26" align="left">
                <input type="text" name="username" maxlength="6" value="" id="username">
              </td>
            </tr>
            <tr>
              <td class="hpb-cnt-tb-cell1" width="130" style="padding-left:15px;" align="left">パスワード</td>
              <td class="hpb-cnt-tb-cell2" height="26" align="left">
                <input type="password" name="password" maxlength="10" value="" id="password">
              </td>
            </tr>
          </tbody>
        </table>
        <div align="center">
          <p>※社員番号・パスワードはAS勤怠管理と同様です。</p>
          <input type="submit" name="login" value="ログイン" id="button_login" style="margin:8px 0px">
          <input type="hidden" name="_csrf" value="1e50b553-b994-4f89-85d0-3e7feb7e9602"/>
          <input type="hidden" name="SSOFlg" value="" id="SSOFlg"/>
          <div class="error_msg">
          
          </div>
          <img width="116" height="31" src="/BaibaiJohoKanri/img/daito_logo.gif" alt="大東建託ロゴ">
        </div>
      </td>
    </tr>
  </table>
  <div>
<input type="hidden" name="_csrf" value="1e50b553-b994-4f89-85d0-3e7feb7e9602" />
</div></form>
</body>
</html>
