@charset "utf-8";
	/* 外字用Webフォント */
	@font-face {
		font-family: 'EUDC';
		src:url('./EUDC.woff') format('woff');
	}

body {
    font-family: "EUDC";
}

/*==========================================================*/
/* 一覧画面の構成 */
    .list-container {
      width: 90%;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
    }

    .list-main-contents {
      width: 83%;
      padding: 10px;
      background-color: #f0f8ff;
    }

    .list-left-menu {
      width: 17%;
      padding: 10px;
      background-color: #d3d3d3;
    }

    .check_lb {
      text-align:left;
      display:block;
    }

    .ankenList {
        border: 1px solid black;
    }

/*==========================================================*/
/* 登録、詳細画面の構成 */
    .container {
      width: 80%;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      padding-bottom: 73px;
    }
    .contents {
      width: 100%;
      padding: 10px;
      background-color: #f0f8ff;
    }

/*==========================================================*/
/* 詳細画面用 */
    .status_title {
      writing-mode: tb-rl;
      text-align: center;
      vertical-align: middle;
      border: 1px solid black;
      border-top-style:none;
      border-left-style:none;
      border-bottom-style:none;
    }
/* カレンダー用 */
    img {
    overflow-clip-margin: content-box;
    overflow: clip;
    }

    img.ui-datepicker-trigger {
    vertical-align: middle;
    width: 24px;
    height: 24px;
    margin-top: -3px;
    margin-left: 3px;
    }

 /* フッター固定 */
    footer {
      margin: 0 auto;
      position: fixed;
      bottom: 0;
      right:140px;
      left:140px;
      padding:0 10px 10px 10px;
      background: #b3dbff;
}
    .commentText{
      font-size: 12px;
    }

/*==========================================================*/
/* レントロール画面の構成 */
    .rentroll-container {
      width: 98%;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
    }
    .rentroll-contents {
      width: 100%;
      padding: 10px;
      background-color: #f0f8ff;
    }
    .rentroll-font {
        font-size: 95%;
    }
    .rentroll-table-thead{
        display: block;
    }
    .rentroll-table-tbody {
        display: block;
        overflow-y: auto;
        overflow-x: hidden;
        max-height: 410px;
        border-spacing: 2px;
    }
    .expenditure-table-tbody {
        display: block;
        overflow-y: auto;
        overflow-x: hidden;
        max-height: 250px;
        border-spacing: 2px;
    }
/*==========================================================*/
/* 共通定義 */
    .table {
        table-layout: fixed;
        width: 100%;
    }

    .width_title_fix {
        width: 60px;
    }

    .width10 {
        width: 10%;
    }

    .width20 {
        width: 20%;
    }

    .input-pull-down {
      width: 170px;
    }

    .input-code-text {
      width: 100px;
    }

    .input-rentroll {
      width: 50px;
    }

    input:readonly {
      color: #595959FF;
      background-color: #59595933;
    }

    .output-code-text {
      width: 100px;
      border: none;
      background-color: #f0f8ff;
    }

    .thead-rentroll-24 {
    width: 24px;
    }
    .thead-rentroll-44 {
    width: 44px;
    }
    .thead-rentroll-60 {
    width: 60px;
    }
    .thead-rentroll-64 {
    width: 64px;
    }
    .thead-rentroll-70 {
    width: 70px;
    }
    .thead-rentroll-74 {
    width: 74px;
    }
    .thead-rentroll-80 {
    width: 80px;
    }
    .thead-rentroll-104 {
    width: 104px;
    }
    .thead-rentroll-173 {
    width: 173px;
    }
    .thead-rentroll-244 {
    width: 244px;
    }

    .output-rentroll {
      border: none;
      background-color: white;
    }
    .output-rentroll[readonly] {
      border: none;
      background-color: #f0f8ff;
    }
    
    .output-rentroll-first-row {
      background-color: #b3dbff;
    }

    .output-rentroll-20 {
      width: 20px;
      border: none;
      background-color: white;
    }
    .output-rentroll-20[readonly] {
      width: 20px;
      border: none;
      background-color: #f0f8ff;
    }

    .output-rentroll-40 {
      width: 40px;
      border: none;
      background-color: white;
    }
    .output-rentroll-40[readonly] {
      width: 40px;
      border: none;
      background-color: #f0f8ff;
    }

    .output-rentroll-60 {
      width: 60px;
      border: none;
      background-color: white;
    }
    .output-rentroll-60[readonly] {
      width: 60px;
      border: none;
      background-color: #f0f8ff;
    }

    .output-rentroll-70 {
      width: 70px;
      border: none;
      background-color: white;
    }
    .output-rentroll-70[readonly] {
      width: 70px;
      border: none;
      background-color: #f0f8ff;
    }

    .output-rentroll-80 {
      width: 80px;
      border: none;
      background-color: white;
    }
    .output-rentroll-80[readonly] {
      width: 80px;
      border: none;
      background-color: #f0f8ff;
    }

    .output-rentroll-100 {
      width: 100px;
      border: none;
      background-color: white;
    }
    .output-rentroll-100[readonly] {
      width: 100px;
      border: none;
      background-color: #f0f8ff;
    }

    .output-text {
       border: none;
       background-color: #f0f8ff;
    }

    .list-button {
      width: 100px;
    }

    .detail-button {
      width: 150px;
    }

    .add-button {
      width: 130px;
    }

    .rentroll-button {
      width: 130px;
    }

    .error_msg {
      color: #f00;
      margin: 1em auto;
      display: block;
      text-align: center;
    }

