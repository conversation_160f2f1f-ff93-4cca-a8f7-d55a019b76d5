﻿# 定义保存目录和基础域名
$saveFolder = "C:\Users\<USER>\Downloads\tem"
$baseDomain = "http://devap9001/BaibaiJohoKanri/"

# 初始化已访问的 URL 哈希表
$visitedUrls = @{}

# 创建保存目录
New-Item -ItemType Directory -Force -Path $saveFolder | Out-Null

function Sanitize-FileName($url) {
    return ($url -replace "[^\w\d]", "_") + ".html"
}

function Download-Page($url) {
    if ($visitedUrls.ContainsKey($url)) { return }

    try {
        Write-Output "正在下载：$url"
        $response = Invoke-WebRequest -Uri $url
        $html = $response.Content

        # 保存 HTML 到本地
        $fileName = Sanitize-FileName($url)
        $filePath = Join-Path $saveFolder $fileName
        $html | Out-File -Encoding UTF8 -FilePath $filePath

        # 标记已访问
        $visitedUrls[$url] = $fileName

        # 提取内部链接
        $matches = Select-String -InputObject $html -Pattern 'href\s*=\s*"''["'']' -AllMatches
        foreach ($match in $matches.Matches) {
            $href = $match.Groups[1].Value

            # 只处理本站内部链接
            if ($href.StartsWith("/")) {
                $fullUrl = $baseDomain.TrimEnd("/") + $href
                Download-Page $fullUrl
            } elseif ($href.StartsWith($baseDomain)) {
                Download-Page $href
            }
        }

    } catch {
        Write-Warning "下载失败：$url"
    }
}

# 启动爬虫
Download-Page "$baseDomain/BaibaiJohoKanri/"

Write-Output "全部页面下载完成，保存路径：$saveFolder"
